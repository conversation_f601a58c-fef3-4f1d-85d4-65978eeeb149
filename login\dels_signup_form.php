<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Custom user sign-up form for Child Protection Training with Turnstile CAPTCHA.
 *
 * @package    core
 * @subpackage auth
 * @copyright  2024 DELS CPT
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/formslib.php');
require_once($CFG->dirroot.'/user/profile/lib.php');
require_once($CFG->dirroot . '/user/editlib.php');
require_once('lib.php');

class dels_signup_form extends moodleform implements renderable, templatable {

    // IOMAD.
    protected $company;

    public function definition() {
        global $CFG, $SESSION, $PAGE, $DB;

        $mform = $this->_form;

        // Set up Child Protection Training company
        $cpt_company = $DB->get_record('company', ['shortname' => 'CPT']);
        if ($cpt_company) {
            $this->company = $cpt_company;
            $SESSION->company = $cpt_company;
        }

        // Add Turnstile JavaScript API
        $PAGE->requires->js(new moodle_url('https://challenges.cloudflare.com/turnstile/v0/api.js'), true);

        // Username field (always required, not using email as username for CPT)
        $mform->addElement('text', 'username', get_string('username'), 'maxlength="100" size="12" autocapitalize="none"');
        $mform->setType('username', PARAM_RAW);
        $mform->addRule('username', get_string('missingusername'), 'required', null, 'client');

        // Password field
        if (!empty($CFG->passwordpolicy)){
            $mform->addElement('static', 'passwordpolicyinfo', '', print_password_policy());
        }
        $mform->addElement('password', 'password', get_string('password'), [
            'maxlength' => MAX_PASSWORD_CHARACTERS,
            'size' => 12,
            'autocomplete' => 'new-password'
        ]);
        $mform->setType('password', core_user::get_property_type('password'));
        $mform->addRule('password', get_string('missingpassword'), 'required', null, 'client');
        $mform->addRule('password', get_string('maximumchars', '', MAX_PASSWORD_CHARACTERS),
            'maxlength', MAX_PASSWORD_CHARACTERS, 'client');

        // Email fields
        $mform->addElement('text', 'email', get_string('email'), 'maxlength="100" size="25"');
        $mform->setType('email', core_user::get_property_type('email'));
        $mform->addRule('email', get_string('missingemail'), 'required', null, 'client');
        $mform->setForceLtr('email');

        $mform->addElement('text', 'email2', get_string('emailagain'), 'maxlength="100" size="25"');
        $mform->setType('email2', PARAM_RAW_TRIMMED);
        $mform->addRule('email2', get_string('missingemail'), 'required', null, 'server');

        // Name fields
        $namefields = useredit_get_required_name_fields();
        foreach ($namefields as $field) {
            $mform->addElement('text', $field, get_string($field), 'maxlength="100" size="30"');
            $mform->setType($field, core_user::get_property_type('firstname'));
            $stringid = 'missing' . $field;
            if (!get_string_manager()->string_exists($stringid, 'moodle')) {
                $stringid = 'required';
            }
            $mform->addRule($field, get_string($stringid), 'required', null, 'client');
        }

        // Skip city and country fields as requested - they will be set from company defaults

        // Profile fields
        profile_signup_fields($mform);

        // Add CAPTCHA section
        $mform->addElement('header', 'captcha_header', 'Security Verification');

        // Add Turnstile widget as HTML element
        $turnstile_html = html_writer::div('', 'cf-turnstile', [
            'data-sitekey' => '0x4AAAAAAB0lFNocek4gFM2N',
            'data-callback' => 'onTurnstileSuccess',
            'data-error-callback' => 'onTurnstileError'
        ]);

        $mform->addElement('html', $turnstile_html);

        // Add hidden field to store the response token
        $mform->addElement('hidden', 'cf-turnstile-response', '');
        $mform->setType('cf-turnstile-response', PARAM_RAW);

        // Hook for plugins to extend form definition.
        core_login_extend_signup_form($mform);

        // Add "Agree to sitepolicy" controls. By default it is a link to the policy text and a checkbox but
        // it can be implemented differently in custom sitepolicy handlers.
        $manager = new \core_privacy\local\sitepolicy\manager();
        $manager->signup_form($mform);

        // IOMAD company fields - automatically set for CPT
        if (!empty($this->company)) {
            $mform->addElement('hidden', 'companyid', $this->company->id);
            $mform->addElement('hidden', 'code', $this->company->shortname);
            $mform->addElement('hidden', 'departmentid', $this->company->deptid);
            $mform->setType('companyid', PARAM_INT);
            $mform->setType('departmentid', PARAM_INT);
            $mform->setType('code', PARAM_CLEAN);
        }

        // Add JavaScript for handling callbacks and form validation
        $js = "
        window.onTurnstileSuccess = function(token) {
            console.log('Turnstile solved:', token);
            var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
            if (responseField) {
                responseField.value = token;
            }
        };
        
        window.onTurnstileError = function() {
            console.log('Turnstile error');
            var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
            if (responseField) {
                responseField.value = '';
            }
            alert('Please complete the verification.');
        };
        
        // Add form validation to ensure CAPTCHA is completed before submission
        document.addEventListener('DOMContentLoaded', function() {
            var forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
                    if (responseField && !responseField.value) {
                        e.preventDefault();
                        alert('Security verification is required.');
                        return false;
                    }
                });
            });
        });
        ";

        $PAGE->requires->js_init_code($js);

        // Add CSS styles
        $css = "
        .cf-turnstile {
            margin: 10px 0;
            display: flex;
            justify-content: center;
        }
        #fgroup_id_captcha_header {
            margin-top: 20px;
        }
        .form-group .error[data-fieldname=\"captcha_header\"] {
            color: #d32f2f;
            font-weight: bold;
            margin-top: 5px;
        }
        @media (max-width: 768px) {
            .cf-turnstile {
                transform: scale(0.8);
                transform-origin: center;
            }
        }
        ";

        $PAGE->requires->css_theme(new moodle_url('data:text/css;base64,' . base64_encode($css)));

        // buttons
        $this->set_display_vertical();
        $this->add_action_buttons(true, get_string('createaccount'));
    }

    public function definition_after_data(){
        $mform = $this->_form;
        $mform->applyFilter('username', 'trim');

        // Trim required name fields.
        foreach (useredit_get_required_name_fields() as $field) {
            $mform->applyFilter($field, 'trim');
        }
    }

    /**
     * Validate user supplied data on the signup form.
     *
     * @param array $data array of ("fieldname"=>value) of submitted data
     * @param array $files array of uploaded files "element_name"=>tmp_file_path
     * @return array of "element_name"=>"error_description" if there are errors,
     *         or an empty array if everything is OK (true allowed for backwards compatibility too).
     */
    public function validation($data, $files) {
        global $CFG, $SESSION;

        $errors = parent::validation($data, $files);

        // Extend validation for any form extensions from plugins.
        $errors = array_merge($errors, core_login_validate_extend_signup_form($data));

        // Validate CAPTCHA
        $captcha_errors = $this->validate_turnstile_captcha($data);
        $errors = array_merge($errors, $captcha_errors);

        // IOMAD - we don't use email as username for CPT
        // Username is always separate from email

        if (!empty($SESSION->currenteditingcompany)) {
            $errors += \company_user::signup_validate_data($data, $files);
            if (!empty($SESSION->signupuserinothercompany)) {
                redirect(new moodle_url("/login/dels_signup.php"));
                die;
            }
        } else {
            $errors += signup_validate_data($data, $files);
        }

        return $errors;
    }

    /**
     * Validate Turnstile CAPTCHA response.
     * @param array $data The form data
     * @return array Array of validation errors
     */
    private function validate_turnstile_captcha($data) {
        $errors = array();

        // Check if CAPTCHA response is present
        if (empty($data['cf-turnstile-response'])) {
            $errors['captcha_header'] = 'Security verification is required.';
            return $errors;
        }

        // Validate the CAPTCHA response with Cloudflare
        $token = $data['cf-turnstile-response'];
        $secret = '0x4AAAAAAB0lFNiF6L0Px1R62tsHd-GOJSo';
        $remoteip = $_SERVER['HTTP_CF_CONNECTING_IP'] ??
                    $_SERVER['HTTP_X_FORWARDED_FOR'] ??
                    $_SERVER['REMOTE_ADDR'];

        $validation = $this->validate_turnstile_token($token, $secret, $remoteip);

        if (!$validation['success']) {
            $errors['captcha_header'] = 'Security verification failed. Please try again.';
            error_log('Turnstile validation failed: ' . implode(', ', $validation['error-codes'] ?? []));
        }

        return $errors;
    }

    /**
     * Validate Turnstile CAPTCHA token with Cloudflare API.
     * @param string $token The CAPTCHA response token
     * @param string $secret The secret key
     * @param string|null $remoteip The user's IP address
     * @return array The validation response
     */
    private function validate_turnstile_token($token, $secret, $remoteip = null) {
        $url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';

        $data = [
            'secret' => $secret,
            'response' => $token
        ];

        if ($remoteip) {
            $data['remoteip'] = $remoteip;
        }

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];

        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);

        if ($response === FALSE) {
            return ['success' => false, 'error-codes' => ['internal-error']];
        }

        return json_decode($response, true);
    }

    /**
     * Export this data so it can be used as the context for a mustache template.
     *
     * @param renderer_base $output Used to do a final render of any components that need to be rendered for export.
     * @return array
     */
    public function export_for_template(renderer_base $output) {
        ob_start();
        $this->display();
        $formhtml = ob_get_contents();
        ob_end_clean();
        $context = [
            'formhtml' => $formhtml
        ];
        return $context;
    }
}
