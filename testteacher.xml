<?xml version="1.0" encoding="UTF-8"?>
<role><shortname>testteacher</shortname><name>Test Teacher</name><description/><archetype>editingteacher</archetype><contextlevels><level>course</level><level>module</level></contextlevels><allowassign><shortname>teacher</shortname><shortname>student</shortname></allowassign><allowoverride><shortname>teacher</shortname><shortname>student</shortname><shortname>guest</shortname></allowoverride><allowswitch><shortname>teacher</shortname><shortname>student</shortname><shortname>guest</shortname></allowswitch><allowview><shortname>coursecreator</shortname><shortname>editingteacher</shortname><shortname>teacher</shortname><shortname>student</shortname></allowview><permissions><inherit>atto/recordrtc:recordaudio</inherit><inherit>atto/recordrtc:recordvideo</inherit><inherit>auth/iomadoidc:manageconnection</inherit><inherit>auth/iomadoidc:manageconnectionconnect</inherit><inherit>auth/iomadoidc:manageconnectiondisconnect</inherit><inherit>auth/oauth2:managelinkedlogins</inherit><inherit>block/admin_bookmarks:myaddinstance</inherit><inherit>block/badges:myaddinstance</inherit><inherit>block/calendar_month:myaddinstance</inherit><inherit>block/calendar_upcoming:myaddinstance</inherit><inherit>block/comments:myaddinstance</inherit><inherit>block/course_list:myaddinstance</inherit><inherit>block/globalsearch:myaddinstance</inherit><inherit>block/glossary_random:myaddinstance</inherit><inherit>block/html:myaddinstance</inherit><inherit>block/iomad_approve_access:addinstance</inherit><inherit>block/iomad_approve_access:approve</inherit><inherit>block/iomad_approve_access:myaddinstance</inherit><inherit>block/iomad_commerce:add_course</inherit><inherit>block/iomad_commerce:addinstance</inherit><inherit>block/iomad_commerce:admin_view</inherit><inherit>block/iomad_commerce:buyinbulk</inherit><inherit>block/iomad_commerce:buyitnow</inherit><inherit>block/iomad_commerce:delete_course</inherit><inherit>block/iomad_commerce:edit_course</inherit><inherit>block/iomad_commerce:hide_course</inherit><inherit>block/iomad_commerce:manage_default</inherit><inherit>block/iomad_commerce:manage_tags</inherit><inherit>block/iomad_commerce:myaddinstance</inherit><inherit>block/iomad_company_admin:addinstance</inherit><inherit>block/iomad_company_admin:allcompany_user_profiles</inherit><inherit>block/iomad_company_admin:allocate_licenses</inherit><inherit>block/iomad_company_admin:assign_company_manager</inherit><inherit>block/iomad_company_admin:assign_company_reporter</inherit><inherit>block/iomad_company_admin:assign_department_manager</inherit><inherit>block/iomad_company_admin:assign_educator</inherit><inherit>block/iomad_company_admin:assign_groups</inherit><inherit>block/iomad_company_admin:canviewchildren</inherit><inherit>block/iomad_company_admin:classrooms</inherit><inherit>block/iomad_company_admin:classrooms_add</inherit><inherit>block/iomad_company_admin:classrooms_delete</inherit><inherit>block/iomad_company_admin:classrooms_edit</inherit><inherit>block/iomad_company_admin:company_add</inherit><inherit>block/iomad_company_admin:company_add_child</inherit><inherit>block/iomad_company_admin:company_course</inherit><inherit>block/iomad_company_admin:company_course_unenrol</inherit><inherit>block/iomad_company_admin:company_course_users</inherit><inherit>block/iomad_company_admin:company_delete</inherit><inherit>block/iomad_company_admin:company_edit</inherit><inherit>block/iomad_company_admin:company_edit_appearance</inherit><inherit>block/iomad_company_admin:company_edit_certificateinfo</inherit><inherit>block/iomad_company_admin:company_edit_restricted</inherit><inherit>block/iomad_company_admin:company_edit_smtp</inherit><inherit>block/iomad_company_admin:company_framework</inherit><inherit>block/iomad_company_admin:company_license_users</inherit><inherit>block/iomad_company_admin:company_manager</inherit><inherit>block/iomad_company_admin:company_template</inherit><inherit>block/iomad_company_admin:company_user</inherit><inherit>block/iomad_company_admin:company_user_profiles</inherit><inherit>block/iomad_company_admin:company_view</inherit><inherit>block/iomad_company_admin:company_view_all</inherit><inherit>block/iomad_company_admin:companyadvancedsettings</inherit><inherit>block/iomad_company_admin:companymanagement_view</inherit><inherit>block/iomad_company_admin:competencymanagement_view</inherit><inherit>block/iomad_company_admin:competencyview</inherit><inherit>block/iomad_company_admin:configiomadoidc</inherit><inherit>block/iomad_company_admin:configiomadoidcsync</inherit><inherit>block/iomad_company_admin:configiomadsaml2</inherit><inherit>block/iomad_company_admin:configmfa</inherit><inherit>block/iomad_company_admin:configpolicies</inherit><inherit>block/iomad_company_admin:coursemanagement_view</inherit><inherit>block/iomad_company_admin:createcourse</inherit><inherit>block/iomad_company_admin:delegatecourse</inherit><inherit>block/iomad_company_admin:deleteallcourses</inherit><inherit>block/iomad_company_admin:deletecourses</inherit><inherit>block/iomad_company_admin:deleteuser</inherit><inherit>block/iomad_company_admin:destroycourses</inherit><inherit>block/iomad_company_admin:downloadcertificates</inherit><inherit>block/iomad_company_admin:downloadmycertificates</inherit><inherit>block/iomad_company_admin:edit_all_departments</inherit><inherit>block/iomad_company_admin:edit_departments</inherit><inherit>block/iomad_company_admin:edit_groups</inherit><inherit>block/iomad_company_admin:edit_licenses</inherit><inherit>block/iomad_company_admin:edit_my_licenses</inherit><inherit>block/iomad_company_admin:editallusers</inherit><inherit>block/iomad_company_admin:editmanagers</inherit><inherit>block/iomad_company_admin:editpubliclocation</inherit><inherit>block/iomad_company_admin:edituserpassword</inherit><inherit>block/iomad_company_admin:editusers</inherit><inherit>block/iomad_company_admin:export_departments</inherit><inherit>block/iomad_company_admin:hideshowallcourses</inherit><inherit>block/iomad_company_admin:hideshowcourses</inherit><inherit>block/iomad_company_admin:import_departments</inherit><inherit>block/iomad_company_admin:licensemanagement_view</inherit><inherit>block/iomad_company_admin:manageallcourses</inherit><inherit>block/iomad_company_admin:managecourses</inherit><inherit>block/iomad_company_admin:manageframeworks</inherit><inherit>block/iomad_company_admin:managetemplates</inherit><inherit>block/iomad_company_admin:myaddinstance</inherit><inherit>block/iomad_company_admin:restrict_capabilities</inherit><inherit>block/iomad_company_admin:split_my_licenses</inherit><inherit>block/iomad_company_admin:suspendcompanies</inherit><inherit>block/iomad_company_admin:suspenduser</inherit><inherit>block/iomad_company_admin:templateview</inherit><inherit>block/iomad_company_admin:unallocate_licenses</inherit><inherit>block/iomad_company_admin:user_create</inherit><inherit>block/iomad_company_admin:user_upload</inherit><inherit>block/iomad_company_admin:usermanagement_view</inherit><inherit>block/iomad_company_admin:view_editusers</inherit><inherit>block/iomad_company_admin:view_licenses</inherit><inherit>block/iomad_company_admin:view_my_company_email</inherit><inherit>block/iomad_company_admin:viewallsharedcourses</inherit><inherit>block/iomad_company_admin:viewcourses</inherit><inherit>block/iomad_company_admin:viewsuspendedusers</inherit><inherit>block/iomad_company_selector:addinstance</inherit><inherit>block/iomad_company_selector:myaddinstance</inherit><inherit>block/iomad_html:myaddinstance</inherit><inherit>block/iomad_learningpath:myaddinstance</inherit><inherit>block/iomad_link:addinstance</inherit><inherit>block/iomad_link:myaddinstance</inherit><inherit>block/iomad_link:view</inherit><inherit>block/iomad_microlearning:addinstance</inherit><inherit>block/iomad_microlearning:assign_threads</inherit><inherit>block/iomad_microlearning:edit_nuggets</inherit><inherit>block/iomad_microlearning:edit_threads</inherit><inherit>block/iomad_microlearning:import_threads</inherit><inherit>block/iomad_microlearning:importgroupfromcsv</inherit><inherit>block/iomad_microlearning:manage_groups</inherit><inherit>block/iomad_microlearning:myaddinstance</inherit><inherit>block/iomad_microlearning:thread_clone</inherit><inherit>block/iomad_microlearning:thread_delete</inherit><inherit>block/iomad_microlearning:thread_view</inherit><inherit>block/iomad_microlearning:view</inherit><inherit>block/iomad_onlineusers:myaddinstance</inherit><inherit>block/iomad_reports:addinstance</inherit><inherit>block/iomad_reports:myaddinstance</inherit><inherit>block/iomad_reports:view</inherit><inherit>block/iomad_welcome:addinstance</inherit><inherit>block/iomad_welcome:myaddinstance</inherit><inherit>block/iomad_welcome:view</inherit><inherit>block/lp:myaddinstance</inherit><inherit>block/mentees:myaddinstance</inherit><inherit>block/mnet_hosts:myaddinstance</inherit><inherit>block/mycourses:myaddinstance</inherit><inherit>block/myoverview:myaddinstance</inherit><inherit>block/myprofile:myaddinstance</inherit><inherit>block/navigation:myaddinstance</inherit><inherit>block/news_items:myaddinstance</inherit><inherit>block/online_users:myaddinstance</inherit><inherit>block/private_files:myaddinstance</inherit><inherit>block/recent_activity:viewaddupdatemodule</inherit><inherit>block/recent_activity:viewdeletemodule</inherit><inherit>block/recentlyaccessedcourses:myaddinstance</inherit><inherit>block/recentlyaccesseditems:myaddinstance</inherit><inherit>block/rss_client:manageanyfeeds</inherit><inherit>block/rss_client:myaddinstance</inherit><inherit>block/settings:myaddinstance</inherit><inherit>block/starredcourses:myaddinstance</inherit><inherit>block/tags:myaddinstance</inherit><inherit>block/timeline:myaddinstance</inherit><inherit>booktool/exportimscp:export</inherit><inherit>enrol/category:synchronised</inherit><inherit>enrol/cohort:unenrol</inherit><inherit>enrol/database:unenrol</inherit><inherit>enrol/fee:config</inherit><inherit>enrol/fee:unenrol</inherit><inherit>enrol/fee:unenrolself</inherit><inherit>enrol/flatfile:manage</inherit><inherit>enrol/flatfile:unenrol</inherit><inherit>enrol/ldap:manage</inherit><inherit>enrol/license:unenrolself</inherit><inherit>enrol/manual:config</inherit><inherit>enrol/manual:unenrolself</inherit><inherit>enrol/meta:selectaslinked</inherit><inherit>enrol/meta:unenrol</inherit><inherit>enrol/paypal:config</inherit><inherit>enrol/paypal:unenrol</inherit><inherit>enrol/paypal:unenrolself</inherit><inherit>enrol/self:enrolself</inherit><inherit>enrol/self:holdkey</inherit><inherit>enrol/self:unenrolself</inherit><inherit>factor/capability:cannotpassfactor</inherit><inherit>gradeexport/ods:publish</inherit><inherit>gradeexport/txt:publish</inherit><inherit>gradeexport/xls:publish</inherit><inherit>gradeexport/xml:publish</inherit><inherit>gradeimport/xml:publish</inherit><inherit>gradereport/overview:view</inherit><inherit>local/dels_resourcecorner:manage</inherit><inherit>local/dels_resourcecorner:view</inherit><inherit>local/email:add</inherit><inherit>local/email:delete</inherit><inherit>local/email:edit</inherit><inherit>local/email:list</inherit><inherit>local/email:send</inherit><inherit>local/email:templateset_list</inherit><inherit>local/iomad_learningpath:manage</inherit><inherit>local/iomad_learningpath:view</inherit><inherit>local/iomad_oidc_sync:manage</inherit><inherit>local/iomad_oidc_sync:view</inherit><inherit>local/iomad_settings:addinstance</inherit><inherit>local/iomad_track:importfrommoodle</inherit><inherit>local/iomadcustompage:edit</inherit><inherit>local/iomadcustompage:editall</inherit><inherit>local/iomadcustompage:view</inherit><inherit>local/report_attendance:view</inherit><inherit>local/report_companies:view</inherit><inherit>local/report_completion_monthly:view</inherit><inherit>local/report_completion_overview:view</inherit><inherit>local/report_completion:view</inherit><inherit>local/report_emails:resend</inherit><inherit>local/report_emails:view</inherit><inherit>local/report_license_usage:view</inherit><inherit>local/report_user_license_allocations:view</inherit><inherit>local/report_user_logins:view</inherit><inherit>local/report_users:addentry</inherit><inherit>local/report_users:clearentries</inherit><inherit>local/report_users:deleteentries</inherit><inherit>local/report_users:deleteentriesfull</inherit><inherit>local/report_users:redocertificates</inherit><inherit>local/report_users:updateentries</inherit><inherit>local/report_users:view</inherit><inherit>message/airnotifier:managedevice</inherit><inherit>mod/assign:editothersubmission</inherit><inherit>mod/assign:submit</inherit><inherit>mod/assign:viewblinddetails</inherit><inherit>mod/assign:viewownsubmissionsummary</inherit><inherit>mod/chat:view</inherit><inherit>mod/choice:view</inherit><inherit>mod/data:manageuserpresets</inherit><inherit>mod/feedback:complete</inherit><inherit>mod/feedback:mapcourse</inherit><inherit>mod/folder:view</inherit><inherit>mod/forum:cantogglefavourite</inherit><inherit>mod/h5pactivity:submit</inherit><inherit>mod/hvp:contenthubregistration</inherit><inherit>mod/hvp:emailconfirmsubmission</inherit><inherit>mod/hvp:emailnotifysubmission</inherit><inherit>mod/hvp:installrecommendedh5plibraries</inherit><inherit>mod/hvp:restrictlibraries</inherit><inherit>mod/hvp:saveresults</inherit><inherit>mod/hvp:updatelibraries</inherit><inherit>mod/hvp:userestrictedlibraries</inherit><inherit>mod/hvp:viewresults</inherit><inherit>mod/imscp:view</inherit><inherit>mod/iomadcertificate:viewother</inherit><inherit>mod/label:view</inherit><inherit>mod/lesson:view</inherit><inherit>mod/lti:admin</inherit><inherit>mod/page:view</inherit><inherit>mod/quiz:attempt</inherit><inherit>mod/quiz:emailconfirmsubmission</inherit><inherit>mod/quiz:emailnotifyattemptgraded</inherit><inherit>mod/quiz:emailnotifysubmission</inherit><inherit>mod/quiz:emailwarnoverdue</inherit><inherit>mod/quiz:ignoretimelimits</inherit><inherit>mod/quiz:reviewmyattempts</inherit><inherit>mod/resource:view</inherit><inherit>mod/scorm:deleteownresponses</inherit><inherit>mod/scorm:skipview</inherit><inherit>mod/url:view</inherit><inherit>mod/workshop:peerassess</inherit><inherit>mod/workshop:submit</inherit><inherit>moodle/ai:acceptpolicy</inherit><inherit>moodle/ai:fetchanyuserpolicystatus</inherit><inherit>moodle/ai:fetchpolicy</inherit><inherit>moodle/analytics:listowninsights</inherit><inherit>moodle/analytics:managemodels</inherit><inherit>moodle/backup:anonymise</inherit><inherit>moodle/backup:userinfo</inherit><inherit>moodle/badges:earnbadge</inherit><inherit>moodle/badges:manageglobalsettings</inherit><inherit>moodle/badges:manageownbadges</inherit><inherit>moodle/badges:viewbadges</inherit><inherit>moodle/badges:viewotherbadges</inherit><inherit>moodle/blog:create</inherit><inherit>moodle/blog:viewdrafts</inherit><inherit>moodle/calendar:manageownentries</inherit><inherit>moodle/category:manage</inherit><inherit>moodle/category:viewcourselist</inherit><inherit>moodle/category:viewhiddencategories</inherit><inherit>moodle/cohort:assign</inherit><inherit>moodle/cohort:configurecustomfields</inherit><inherit>moodle/cohort:manage</inherit><inherit>moodle/competency:competencymanage</inherit><inherit>moodle/competency:competencyview</inherit><inherit>moodle/competency:coursecompetencyconfigure</inherit><inherit>moodle/competency:coursecompetencygradable</inherit><inherit>moodle/competency:coursecompetencyview</inherit><inherit>moodle/competency:evidencedelete</inherit><inherit>moodle/competency:plancomment</inherit><inherit>moodle/competency:plancommentown</inherit><inherit>moodle/competency:planmanage</inherit><inherit>moodle/competency:planmanagedraft</inherit><inherit>moodle/competency:planmanageown</inherit><inherit>moodle/competency:planmanageowndraft</inherit><inherit>moodle/competency:planrequestreview</inherit><inherit>moodle/competency:planrequestreviewown</inherit><inherit>moodle/competency:planreview</inherit><inherit>moodle/competency:planview</inherit><inherit>moodle/competency:planviewdraft</inherit><inherit>moodle/competency:planviewown</inherit><inherit>moodle/competency:planviewowndraft</inherit><inherit>moodle/competency:templatemanage</inherit><inherit>moodle/competency:templateview</inherit><inherit>moodle/competency:usercompetencycomment</inherit><inherit>moodle/competency:usercompetencycommentown</inherit><inherit>moodle/competency:usercompetencyrequestreview</inherit><inherit>moodle/competency:usercompetencyrequestreviewown</inherit><inherit>moodle/competency:usercompetencyreview</inherit><inherit>moodle/competency:userevidencemanage</inherit><inherit>moodle/competency:userevidencemanageown</inherit><inherit>moodle/competency:userevidenceview</inherit><inherit>moodle/contentbank:changelockedcustomfields</inherit><inherit>moodle/contentbank:configurecustomfields</inherit><inherit>moodle/contentbank:copyanycontent</inherit><inherit>moodle/contentbank:deleteanycontent</inherit><inherit>moodle/contentbank:deleteowncontent</inherit><inherit>moodle/contentbank:manageanycontent</inherit><inherit>moodle/contentbank:viewunlistedcontent</inherit><inherit>moodle/course:changelockedcustomfields</inherit><inherit>moodle/course:configurecustomfields</inherit><inherit>moodle/course:create</inherit><inherit>moodle/course:delete</inherit><inherit>moodle/course:ignorefilesizelimits</inherit><inherit>moodle/course:isincompletionreports</inherit><inherit>moodle/course:recommendactivity</inherit><inherit>moodle/course:request</inherit><inherit>moodle/course:togglecompletion</inherit><inherit>moodle/course:view</inherit><inherit>moodle/grade:managesharedforms</inherit><inherit>moodle/grade:sharegradingforms</inherit><inherit>moodle/grade:view</inherit><inherit>moodle/group:configurecustomfields</inherit><inherit>moodle/h5p:updatelibraries</inherit><inherit>moodle/my:configsyspages</inherit><inherit>moodle/my:manageblocks</inherit><inherit>moodle/payment:manageaccounts</inherit><inherit>moodle/payment:viewpayments</inherit><inherit>moodle/question:config</inherit><inherit>moodle/reportbuilder:edit</inherit><inherit>moodle/reportbuilder:editall</inherit><inherit>moodle/reportbuilder:scheduleviewas</inherit><inherit>moodle/reportbuilder:view</inherit><inherit>moodle/reportbuilder:viewall</inherit><inherit>moodle/restore:createuser</inherit><inherit>moodle/restore:rolldates</inherit><inherit>moodle/restore:userinfo</inherit><inherit>moodle/role:manage</inherit><inherit>moodle/role:override</inherit><inherit>moodle/site:approvecourse</inherit><inherit>moodle/site:config</inherit><inherit>moodle/site:configview</inherit><inherit>moodle/site:deleteanymessage</inherit><inherit>moodle/site:deleteownmessage</inherit><inherit>moodle/site:forcelanguage</inherit><inherit>moodle/site:maintenanceaccess</inherit><inherit>moodle/site:manageallmessaging</inherit><inherit>moodle/site:managecontextlocks</inherit><inherit>moodle/site:mnetlogintoremote</inherit><inherit>moodle/site:senderrormessage</inherit><inherit>moodle/site:sendmessage</inherit><inherit>moodle/site:uploadusers</inherit><inherit>moodle/site:viewanonymousevents</inherit><inherit>moodle/site:viewparticipants</inherit><inherit>moodle/tag:edit</inherit><inherit>moodle/tag:flag</inherit><inherit>moodle/tag:manage</inherit><inherit>moodle/user:changeownpassword</inherit><inherit>moodle/user:create</inherit><inherit>moodle/user:delete</inherit><inherit>moodle/user:editmessageprofile</inherit><inherit>moodle/user:editownmessageprofile</inherit><inherit>moodle/user:editownprofile</inherit><inherit>moodle/user:editprofile</inherit><inherit>moodle/user:ignoreuserquota</inherit><inherit>moodle/user:loginas</inherit><inherit>moodle/user:manageblocks</inherit><inherit>moodle/user:manageownblocks</inherit><inherit>moodle/user:manageownfiles</inherit><inherit>moodle/user:managesyspages</inherit><inherit>moodle/user:update</inherit><inherit>moodle/user:viewalldetails</inherit><inherit>moodle/user:viewlastip</inherit><inherit>moodle/user:viewprofilepictures</inherit><inherit>moodle/user:viewuseractivitiesreport</inherit><inherit>moodle/webservice:createmobiletoken</inherit><inherit>moodle/webservice:createtoken</inherit><inherit>moodle/webservice:managealltokens</inherit><inherit>qbank/customfields:changelockedcustomfields</inherit><inherit>qbank/customfields:configurecustomfields</inherit><inherit>quizaccess/seb:managetemplates</inherit><inherit>report/performance:view</inherit><inherit>report/questioninstances:view</inherit><inherit>report/security:view</inherit><inherit>report/status:view</inherit><inherit>report/usersessions:manageownsessions</inherit><inherit>repository/areafiles:view</inherit><inherit>repository/contentbank:accesscoursecategorycontent</inherit><inherit>repository/contentbank:accessgeneralcontent</inherit><inherit>repository/dropbox:view</inherit><inherit>repository/equella:view</inherit><inherit>repository/flickr_public:view</inherit><inherit>repository/flickr:view</inherit><inherit>repository/googledocs:view</inherit><inherit>repository/merlot:view</inherit><inherit>repository/nextcloud:view</inherit><inherit>repository/onedrive:view</inherit><inherit>repository/recent:view</inherit><inherit>repository/s3:view</inherit><inherit>repository/upload:view</inherit><inherit>repository/url:view</inherit><inherit>repository/user:view</inherit><inherit>repository/wikimedia:view</inherit><inherit>repository/youtube:view</inherit><inherit>tiny/premium:accesspremium</inherit><inherit>tiny/recordrtc:recordaudio</inherit><inherit>tiny/recordrtc:recordscreen</inherit><inherit>tiny/recordrtc:recordvideo</inherit><inherit>tool/brickfield:viewsystemtools</inherit><inherit>tool/customlang:edit</inherit><inherit>tool/customlang:export</inherit><inherit>tool/customlang:view</inherit><inherit>tool/dataprivacy:downloadallrequests</inherit><inherit>tool/dataprivacy:downloadownrequest</inherit><inherit>tool/dataprivacy:makedatadeletionrequestsforchildren</inherit><inherit>tool/dataprivacy:makedatarequestsforchildren</inherit><inherit>tool/dataprivacy:managedataregistry</inherit><inherit>tool/dataprivacy:managedatarequests</inherit><inherit>tool/dataprivacy:requestdelete</inherit><inherit>tool/dataprivacy:requestdeleteforotheruser</inherit><inherit>tool/iomadmerge:iomadmerge</inherit><inherit>tool/iomadpolicy:accept</inherit><inherit>tool/iomadpolicy:acceptbehalf</inherit><inherit>tool/iomadpolicy:managedocs</inherit><inherit>tool/iomadpolicy:viewacceptances</inherit><inherit>tool/lpmigrate:frameworksmigrate</inherit><inherit>tool/mfa:mfaaccess</inherit><inherit>tool/monitor:managetool</inherit><inherit>tool/policy:accept</inherit><inherit>tool/policy:acceptbehalf</inherit><inherit>tool/policy:managedocs</inherit><inherit>tool/policy:viewacceptances</inherit><inherit>tool/redocerts:redocertificates</inherit><inherit>tool/uploadcourse:use</inherit><inherit>tool/uploaduser:uploaduserpictures</inherit><inherit>tool/usertours:managetours</inherit><inherit>webservice/rest:use</inherit><inherit>webservice/soap:use</inherit><allow>aiplacement/courseassist:summarise_text</allow><allow>aiplacement/editor:generate_image</allow><allow>aiplacement/editor:generate_text</allow><allow>atto/h5p:addembed</allow><allow>block/accessreview:addinstance</allow><allow>block/accessreview:view</allow><allow>block/activity_modules:addinstance</allow><allow>block/activity_results:addinstance</allow><allow>block/admin_bookmarks:addinstance</allow><allow>block/badges:addinstance</allow><allow>block/blog_menu:addinstance</allow><allow>block/blog_recent:addinstance</allow><allow>block/blog_tags:addinstance</allow><allow>block/calendar_month:addinstance</allow><allow>block/calendar_upcoming:addinstance</allow><allow>block/comments:addinstance</allow><allow>block/completionstatus:addinstance</allow><allow>block/course_list:addinstance</allow><allow>block/course_summary:addinstance</allow><allow>block/feedback:addinstance</allow><allow>block/globalsearch:addinstance</allow><allow>block/glossary_random:addinstance</allow><allow>block/html:addinstance</allow><allow>block/iomad_html:addinstance</allow><allow>block/iomad_learningpath:addinstance</allow><allow>block/iomad_onlineusers:addinstance</allow><allow>block/iomad_onlineusers:viewlist</allow><allow>block/login:addinstance</allow><allow>block/lp:addinstance</allow><allow>block/mentees:addinstance</allow><allow>block/mnet_hosts:addinstance</allow><allow>block/mycourses:addinstance</allow><allow>block/myprofile:addinstance</allow><allow>block/navigation:addinstance</allow><allow>block/news_items:addinstance</allow><allow>block/online_users:addinstance</allow><allow>block/online_users:viewlist</allow><allow>block/private_files:addinstance</allow><allow>block/recent_activity:addinstance</allow><allow>block/rss_client:addinstance</allow><allow>block/rss_client:manageownfeeds</allow><allow>block/search_forums:addinstance</allow><allow>block/section_links:addinstance</allow><allow>block/selfcompletion:addinstance</allow><allow>block/settings:addinstance</allow><allow>block/site_main_menu:addinstance</allow><allow>block/social_activities:addinstance</allow><allow>block/tag_flickr:addinstance</allow><allow>block/tag_youtube:addinstance</allow><allow>block/tags:addinstance</allow><allow>booktool/importhtml:import</allow><allow>booktool/print:print</allow><allow>communication/matrix:moderator</allow><allow>contenttype/h5p:access</allow><allow>contenttype/h5p:upload</allow><allow>contenttype/h5p:useeditor</allow><allow>enrol/category:config</allow><allow>enrol/cohort:config</allow><allow>enrol/database:config</allow><allow>enrol/fee:manage</allow><allow>enrol/guest:config</allow><allow>enrol/imsenterprise:config</allow><allow>enrol/license:config</allow><allow>enrol/license:manage</allow><allow>enrol/license:unenrol</allow><allow>enrol/lti:config</allow><allow>enrol/lti:unenrol</allow><allow>enrol/manual:enrol</allow><allow>enrol/manual:manage</allow><allow>enrol/manual:unenrol</allow><allow>enrol/meta:config</allow><allow>enrol/mnet:config</allow><allow>enrol/paypal:manage</allow><allow>enrol/self:config</allow><allow>enrol/self:manage</allow><allow>enrol/self:unenrol</allow><allow>forumreport/summary:view</allow><allow>forumreport/summary:viewall</allow><allow>gradeexport/ods:view</allow><allow>gradeexport/txt:view</allow><allow>gradeexport/xls:view</allow><allow>gradeexport/xml:view</allow><allow>gradeimport/csv:view</allow><allow>gradeimport/direct:view</allow><allow>gradeimport/xml:view</allow><allow>gradereport/grader:view</allow><allow>gradereport/history:view</allow><allow>gradereport/outcomes:view</allow><allow>gradereport/singleview:view</allow><allow>gradereport/summary:view</allow><allow>gradereport/user:view</allow><allow>mod/assign:addinstance</allow><allow>mod/assign:exportownsubmission</allow><allow>mod/assign:grade</allow><allow>mod/assign:grantextension</allow><allow>mod/assign:manageallocations</allow><allow>mod/assign:managegrades</allow><allow>mod/assign:manageoverrides</allow><allow>mod/assign:receivegradernotifications</allow><allow>mod/assign:releasegrades</allow><allow>mod/assign:revealidentities</allow><allow>mod/assign:reviewgrades</allow><allow>mod/assign:showhiddengrader</allow><allow>mod/assign:view</allow><allow>mod/assign:viewgrades</allow><allow>mod/bigbluebuttonbn:addinstance</allow><allow>mod/bigbluebuttonbn:addinstancewithmeeting</allow><allow>mod/bigbluebuttonbn:addinstancewithrecording</allow><allow>mod/bigbluebuttonbn:deleterecordings</allow><allow>mod/bigbluebuttonbn:importrecordings</allow><allow>mod/bigbluebuttonbn:join</allow><allow>mod/bigbluebuttonbn:managerecordings</allow><allow>mod/bigbluebuttonbn:protectrecordings</allow><allow>mod/bigbluebuttonbn:publishrecordings</allow><allow>mod/bigbluebuttonbn:seepresentation</allow><allow>mod/bigbluebuttonbn:unprotectrecordings</allow><allow>mod/bigbluebuttonbn:unpublishrecordings</allow><allow>mod/bigbluebuttonbn:view</allow><allow>mod/bigbluebuttonbn:viewallrecordingformats</allow><allow>mod/book:addinstance</allow><allow>mod/book:edit</allow><allow>mod/book:read</allow><allow>mod/book:viewhiddenchapters</allow><allow>mod/chat:addinstance</allow><allow>mod/chat:chat</allow><allow>mod/chat:deletelog</allow><allow>mod/chat:exportparticipatedsession</allow><allow>mod/chat:exportsession</allow><allow>mod/chat:readlog</allow><allow>mod/choice:addinstance</allow><allow>mod/choice:choose</allow><allow>mod/choice:deleteresponses</allow><allow>mod/choice:downloadresponses</allow><allow>mod/choice:readresponses</allow><allow>mod/data:addinstance</allow><allow>mod/data:approve</allow><allow>mod/data:comment</allow><allow>mod/data:exportallentries</allow><allow>mod/data:exportentry</allow><allow>mod/data:exportownentry</allow><allow>mod/data:exportuserinfo</allow><allow>mod/data:managecomments</allow><allow>mod/data:manageentries</allow><allow>mod/data:managetemplates</allow><allow>mod/data:rate</allow><allow>mod/data:view</allow><allow>mod/data:viewallratings</allow><allow>mod/data:viewalluserpresets</allow><allow>mod/data:viewanyrating</allow><allow>mod/data:viewentry</allow><allow>mod/data:viewrating</allow><allow>mod/data:writeentry</allow><allow>mod/feedback:addinstance</allow><allow>mod/feedback:createprivatetemplate</allow><allow>mod/feedback:createpublictemplate</allow><allow>mod/feedback:deletesubmissions</allow><allow>mod/feedback:deletetemplate</allow><allow>mod/feedback:edititems</allow><allow>mod/feedback:receivemail</allow><allow>mod/feedback:view</allow><allow>mod/feedback:viewanalysepage</allow><allow>mod/feedback:viewreports</allow><allow>mod/folder:addinstance</allow><allow>mod/folder:managefiles</allow><allow>mod/forum:addinstance</allow><allow>mod/forum:addnews</allow><allow>mod/forum:addquestion</allow><allow>mod/forum:allowforcesubscribe</allow><allow>mod/forum:canmailnow</allow><allow>mod/forum:canoverridecutoff</allow><allow>mod/forum:canoverridediscussionlock</allow><allow>mod/forum:canposttomygroups</allow><allow>mod/forum:createattachment</allow><allow>mod/forum:deleteanypost</allow><allow>mod/forum:deleteownpost</allow><allow>mod/forum:editanypost</allow><allow>mod/forum:exportdiscussion</allow><allow>mod/forum:exportforum</allow><allow>mod/forum:exportownpost</allow><allow>mod/forum:exportpost</allow><allow>mod/forum:grade</allow><allow>mod/forum:managesubscriptions</allow><allow>mod/forum:movediscussions</allow><allow>mod/forum:pindiscussions</allow><allow>mod/forum:postprivatereply</allow><allow>mod/forum:postwithoutthrottling</allow><allow>mod/forum:rate</allow><allow>mod/forum:readprivatereplies</allow><allow>mod/forum:replynews</allow><allow>mod/forum:replypost</allow><allow>mod/forum:splitdiscussions</allow><allow>mod/forum:startdiscussion</allow><allow>mod/forum:viewallratings</allow><allow>mod/forum:viewanyrating</allow><allow>mod/forum:viewdiscussion</allow><allow>mod/forum:viewhiddentimedposts</allow><allow>mod/forum:viewqandawithoutposting</allow><allow>mod/forum:viewrating</allow><allow>mod/forum:viewsubscribers</allow><allow>mod/glossary:addinstance</allow><allow>mod/glossary:approve</allow><allow>mod/glossary:comment</allow><allow>mod/glossary:export</allow><allow>mod/glossary:exportentry</allow><allow>mod/glossary:exportownentry</allow><allow>mod/glossary:import</allow><allow>mod/glossary:managecategories</allow><allow>mod/glossary:managecomments</allow><allow>mod/glossary:manageentries</allow><allow>mod/glossary:rate</allow><allow>mod/glossary:view</allow><allow>mod/glossary:viewallratings</allow><allow>mod/glossary:viewanyrating</allow><allow>mod/glossary:viewrating</allow><allow>mod/glossary:write</allow><allow>mod/h5pactivity:addinstance</allow><allow>mod/h5pactivity:reviewattempts</allow><allow>mod/h5pactivity:view</allow><allow>mod/hvp:addinstance</allow><allow>mod/hvp:getcachedassets</allow><allow>mod/hvp:getembedcode</allow><allow>mod/hvp:getexport</allow><allow>mod/hvp:manage</allow><allow>mod/hvp:savecontentuserdata</allow><allow>mod/hvp:share</allow><allow>mod/hvp:view</allow><allow>mod/hvp:viewallresults</allow><allow>mod/imscp:addinstance</allow><allow>mod/iomadcertificate:addinstance</allow><allow>mod/iomadcertificate:manage</allow><allow>mod/iomadcertificate:printteacher</allow><allow>mod/iomadcertificate:view</allow><allow>mod/label:addinstance</allow><allow>mod/lesson:addinstance</allow><allow>mod/lesson:edit</allow><allow>mod/lesson:grade</allow><allow>mod/lesson:manage</allow><allow>mod/lesson:manageoverrides</allow><allow>mod/lesson:viewreports</allow><allow>mod/lti:addcoursetool</allow><allow>mod/lti:addinstance</allow><allow>mod/lti:addpreconfiguredinstance</allow><allow>mod/lti:manage</allow><allow>mod/lti:requesttooladd</allow><allow>mod/lti:view</allow><allow>mod/page:addinstance</allow><allow>mod/quiz:addinstance</allow><allow>mod/quiz:deleteattempts</allow><allow>mod/quiz:grade</allow><allow>mod/quiz:manage</allow><allow>mod/quiz:manageoverrides</allow><allow>mod/quiz:preview</allow><allow>mod/quiz:regrade</allow><allow>mod/quiz:reopenattempts</allow><allow>mod/quiz:view</allow><allow>mod/quiz:viewoverrides</allow><allow>mod/quiz:viewreports</allow><allow>mod/resource:addinstance</allow><allow>mod/scorm:addinstance</allow><allow>mod/scorm:deleteresponses</allow><allow>mod/scorm:savetrack</allow><allow>mod/scorm:viewreport</allow><allow>mod/scorm:viewscores</allow><allow>mod/subsection:addinstance</allow><allow>mod/survey:addinstance</allow><allow>mod/survey:download</allow><allow>mod/survey:participate</allow><allow>mod/survey:readresponses</allow><allow>mod/trainingevent:add</allow><allow>mod/trainingevent:addinstance</allow><allow>mod/trainingevent:addoverride</allow><allow>mod/trainingevent:grade</allow><allow>mod/trainingevent:invite</allow><allow>mod/trainingevent:resetattendees</allow><allow>mod/trainingevent:viewallattendees</allow><allow>mod/trainingevent:viewattendees</allow><allow>mod/url:addinstance</allow><allow>mod/wiki:addinstance</allow><allow>mod/wiki:createpage</allow><allow>mod/wiki:editcomment</allow><allow>mod/wiki:editpage</allow><allow>mod/wiki:managecomment</allow><allow>mod/wiki:managefiles</allow><allow>mod/wiki:managewiki</allow><allow>mod/wiki:overridelock</allow><allow>mod/wiki:viewcomment</allow><allow>mod/wiki:viewpage</allow><allow>mod/workshop:addinstance</allow><allow>mod/workshop:allocate</allow><allow>mod/workshop:deletesubmissions</allow><allow>mod/workshop:editdimensions</allow><allow>mod/workshop:exportsubmissions</allow><allow>mod/workshop:ignoredeadlines</allow><allow>mod/workshop:manageexamples</allow><allow>mod/workshop:overridegrades</allow><allow>mod/workshop:publishsubmissions</allow><allow>mod/workshop:switchphase</allow><allow>mod/workshop:view</allow><allow>mod/workshop:viewallassessments</allow><allow>mod/workshop:viewallsubmissions</allow><allow>mod/workshop:viewauthornames</allow><allow>mod/workshop:viewauthorpublished</allow><allow>mod/workshop:viewpublishedsubmissions</allow><allow>mod/workshop:viewreviewernames</allow><allow>moodle/analytics:listinsights</allow><allow>moodle/backup:backupactivity</allow><allow>moodle/backup:backupcourse</allow><allow>moodle/backup:backupsection</allow><allow>moodle/backup:backuptargetimport</allow><allow>moodle/backup:configure</allow><allow>moodle/backup:downloadfile</allow><allow>moodle/badges:awardbadge</allow><allow>moodle/badges:configurecriteria</allow><allow>moodle/badges:configuredetails</allow><allow>moodle/badges:configuremessages</allow><allow>moodle/badges:createbadge</allow><allow>moodle/badges:deletebadge</allow><allow>moodle/badges:revokebadge</allow><allow>moodle/badges:viewawarded</allow><allow>moodle/block:edit</allow><allow>moodle/block:view</allow><allow>moodle/blog:manageentries</allow><allow>moodle/blog:manageexternal</allow><allow>moodle/blog:search</allow><allow>moodle/blog:view</allow><allow>moodle/calendar:manageentries</allow><allow>moodle/calendar:managegroupentries</allow><allow>moodle/cohort:view</allow><allow>moodle/comment:delete</allow><allow>moodle/comment:post</allow><allow>moodle/comment:view</allow><allow>moodle/competency:competencygrade</allow><allow>moodle/competency:coursecompetencymanage</allow><allow>moodle/competency:usercompetencyview</allow><allow>moodle/contentbank:access</allow><allow>moodle/contentbank:copycontent</allow><allow>moodle/contentbank:downloadcontent</allow><allow>moodle/contentbank:manageowncontent</allow><allow>moodle/contentbank:upload</allow><allow>moodle/contentbank:useeditor</allow><allow>moodle/course:activityvisibility</allow><allow>moodle/course:bulkmessaging</allow><allow>moodle/course:changecategory</allow><allow>moodle/course:changefullname</allow><allow>moodle/course:changeidnumber</allow><allow>moodle/course:changeshortname</allow><allow>moodle/course:changesummary</allow><allow>moodle/course:configurecoursecommunication</allow><allow>moodle/course:configuredownloadcontent</allow><allow>moodle/course:creategroupconversations</allow><allow>moodle/course:downloadcoursecontent</allow><allow>moodle/course:editcoursewelcomemessage</allow><allow>moodle/course:enrolconfig</allow><allow>moodle/course:enrolreview</allow><allow>moodle/course:ignoreavailabilityrestrictions</allow><allow>moodle/course:manageactivities</allow><allow>moodle/course:managefiles</allow><allow>moodle/course:managegroups</allow><allow>moodle/course:managescales</allow><allow>moodle/course:markcomplete</allow><allow>moodle/course:movesections</allow><allow>moodle/course:overridecompletion</allow><allow>moodle/course:renameroles</allow><allow>moodle/course:reset</allow><allow>moodle/course:reviewotherusers</allow><allow>moodle/course:sectionvisibility</allow><allow>moodle/course:setcurrentsection</allow><allow>moodle/course:setforcedlanguage</allow><allow>moodle/course:tag</allow><allow>moodle/course:update</allow><allow>moodle/course:useremail</allow><allow>moodle/course:viewhiddenactivities</allow><allow>moodle/course:viewhiddencourses</allow><allow>moodle/course:viewhiddengroups</allow><allow>moodle/course:viewhiddensections</allow><allow>moodle/course:viewhiddenuserfields</allow><allow>moodle/course:viewparticipants</allow><allow>moodle/course:viewscales</allow><allow>moodle/course:viewsuspendedusers</allow><allow>moodle/course:visibility</allow><allow>moodle/filter:manage</allow><allow>moodle/grade:edit</allow><allow>moodle/grade:export</allow><allow>moodle/grade:hide</allow><allow>moodle/grade:import</allow><allow>moodle/grade:lock</allow><allow>moodle/grade:manage</allow><allow>moodle/grade:managegradingforms</allow><allow>moodle/grade:manageletters</allow><allow>moodle/grade:manageoutcomes</allow><allow>moodle/grade:unlock</allow><allow>moodle/grade:viewall</allow><allow>moodle/grade:viewhidden</allow><allow>moodle/h5p:deploy</allow><allow>moodle/h5p:setdisplayoptions</allow><allow>moodle/moodlenet:shareactivity</allow><allow>moodle/moodlenet:sharecourse</allow><allow>moodle/notes:manage</allow><allow>moodle/notes:view</allow><allow>moodle/portfolio:export</allow><allow>moodle/question:add</allow><allow>moodle/question:commentall</allow><allow>moodle/question:commentmine</allow><allow>moodle/question:editall</allow><allow>moodle/question:editmine</allow><allow>moodle/question:flag</allow><allow>moodle/question:managecategory</allow><allow>moodle/question:moveall</allow><allow>moodle/question:movemine</allow><allow>moodle/question:tagall</allow><allow>moodle/question:tagmine</allow><allow>moodle/question:useall</allow><allow>moodle/question:usemine</allow><allow>moodle/question:viewall</allow><allow>moodle/question:viewmine</allow><allow>moodle/rating:rate</allow><allow>moodle/rating:view</allow><allow>moodle/rating:viewall</allow><allow>moodle/rating:viewany</allow><allow>moodle/restore:configure</allow><allow>moodle/restore:restoreactivity</allow><allow>moodle/restore:restorecourse</allow><allow>moodle/restore:restoresection</allow><allow>moodle/restore:restoretargetimport</allow><allow>moodle/restore:uploadfile</allow><allow>moodle/restore:viewautomatedfilearea</allow><allow>moodle/role:assign</allow><allow>moodle/role:review</allow><allow>moodle/role:safeoverride</allow><allow>moodle/role:switchroles</allow><allow>moodle/search:query</allow><allow>moodle/site:accessallgroups</allow><allow>moodle/site:doclinks</allow><allow>moodle/site:manageblocks</allow><allow>moodle/site:messageanyuser</allow><allow>moodle/site:readallmessages</allow><allow>moodle/site:trustcontent</allow><allow>moodle/site:viewfullnames</allow><allow>moodle/site:viewreports</allow><allow>moodle/site:viewuseridentity</allow><allow>moodle/tag:editblocks</allow><allow>moodle/user:readuserblogs</allow><allow>moodle/user:readuserposts</allow><allow>moodle/user:viewdetails</allow><allow>moodle/user:viewhiddendetails</allow><allow>qbank/customfields:viewhiddencustomfields</allow><allow>quiz/grading:viewidnumber</allow><allow>quiz/grading:viewstudentnames</allow><allow>quiz/statistics:view</allow><allow>quizaccess/seb:bypassseb</allow><allow>quizaccess/seb:manage_filemanager_sebconfigfile</allow><allow>quizaccess/seb:manage_seb_activateurlfiltering</allow><allow>quizaccess/seb:manage_seb_allowcapturecamera</allow><allow>quizaccess/seb:manage_seb_allowcapturemicrophone</allow><allow>quizaccess/seb:manage_seb_allowedbrowserexamkeys</allow><allow>quizaccess/seb:manage_seb_allowreloadinexam</allow><allow>quizaccess/seb:manage_seb_allowspellchecking</allow><allow>quizaccess/seb:manage_seb_allowuserquitseb</allow><allow>quizaccess/seb:manage_seb_configuremanually</allow><allow>quizaccess/seb:manage_seb_enableaudiocontrol</allow><allow>quizaccess/seb:manage_seb_expressionsallowed</allow><allow>quizaccess/seb:manage_seb_expressionsblocked</allow><allow>quizaccess/seb:manage_seb_filterembeddedcontent</allow><allow>quizaccess/seb:manage_seb_linkquitseb</allow><allow>quizaccess/seb:manage_seb_muteonstartup</allow><allow>quizaccess/seb:manage_seb_quitpassword</allow><allow>quizaccess/seb:manage_seb_regexallowed</allow><allow>quizaccess/seb:manage_seb_regexblocked</allow><allow>quizaccess/seb:manage_seb_requiresafeexambrowser</allow><allow>quizaccess/seb:manage_seb_showkeyboardlayout</allow><allow>quizaccess/seb:manage_seb_showreloadbutton</allow><allow>quizaccess/seb:manage_seb_showsebdownloadlink</allow><allow>quizaccess/seb:manage_seb_showsebtaskbar</allow><allow>quizaccess/seb:manage_seb_showtime</allow><allow>quizaccess/seb:manage_seb_showwificontrol</allow><allow>quizaccess/seb:manage_seb_templateid</allow><allow>quizaccess/seb:manage_seb_userconfirmquit</allow><allow>quizaccess/seb:manage_seb_usesebclientconfig</allow><allow>report/completion:view</allow><allow>report/courseoverview:view</allow><allow>report/log:view</allow><allow>report/log:viewtoday</allow><allow>report/loglive:view</allow><allow>report/outline:view</allow><allow>report/outline:viewuserreport</allow><allow>report/participation:view</allow><allow>report/progress:view</allow><allow>report/stats:view</allow><allow>repository/contentbank:accesscoursecontent</allow><allow>repository/contentbank:view</allow><allow>repository/coursefiles:view</allow><allow>repository/filesystem:view</allow><allow>repository/local:view</allow><allow>repository/webdav:view</allow><allow>tiny/h5p:addembed</allow><allow>tool/brickfield:viewcoursetools</allow><allow>tool/monitor:managerules</allow><allow>tool/monitor:subscribe</allow><allow>tool/recyclebin:deleteitems</allow><allow>tool/recyclebin:restoreitems</allow><allow>tool/recyclebin:viewitems</allow></permissions></role>
