<?php
defined('MOODLE_INTERNAL') || die();

function local_dels_resourcecorner_pluginfile($course, $cm, $context, $filearea, $args, $forcedownload, array $options=array()) {
    // Check the contextlevel is as expected
    global $DB;
    
    if ($context->contextlevel != CONTEXT_SYSTEM) {
        return false;
    }
    
    // Make sure the filearea is one of those used by the plugin
    if ($filearea !== 'resource') {
        return false;
    }
    
    // Check the user is logged in and has access to the resource corner
    require_login();
    
    // Leave this line out if you set the itemid to null in make_pluginfile_url (set $itemid to 0 instead)
    $itemid = array_shift($args); // The first item in the $args array
    
    // Extract the filename / filepath from the $args array
    $filename = array_pop($args); // The last item in the $args array
    if (!$args) {
        $filepath = '/';
    } else {
        $filepath = '/'.implode('/', $args).'/';
    }
    
    // Retrieve the file from the Files API
    $fs = get_file_storage();
    $file = $fs->get_file($context->id, 'local_dels_resourcecorner', $filearea, $itemid, $filepath, $filename);
    
    if (!$file) {
        return false; // The file does not exist
    }
    
    // Check if the resource allows download
    if ($filearea === 'resource') {
        $resource = $DB->get_record('local_dels_resourcecorner', array('id' => $itemid));
        if (!$resource || !$resource->allow_download) {
            return false; // Download not allowed
        }
    }
    
    // We can now send the file back to the browser
    send_stored_file($file, 0, 0, $forcedownload, $options);
}

function local_dels_resourcecorner_delete_resource($id) {
    global $DB;
    
    $resource = $DB->get_record('local_dels_resourcecorner', array('id' => $id));
    if ($resource) {
        $fs = get_file_storage();
        $file = $fs->get_file_by_id($resource->file_id);
        
        if ($file) {
            $file->delete();
        }
        
        $DB->delete_records('local_dels_resourcecorner', array('id' => $id));
    }
}

function local_dels_resourcecorner_toggle_download($id) {
    global $DB;
    
    $resource = $DB->get_record('local_dels_resourcecorner', array('id' => $id));
    if ($resource) {
        $resource->allow_download = !$resource->allow_download;
        $resource->time_modified = time();
        $DB->update_record('local_dels_resourcecorner', $resource);
    }
}

function local_dels_resourcecorner_get_file($fileid) {
    $fs = get_file_storage();
    return $fs->get_file_by_id($fileid);
}

/**
 * 准备编辑器内容
 */
function local_dels_resourcecorner_prepare_editor_content($content) {
    return array(
        'text' => $content,
        'format' => FORMAT_HTML,
        'itemid' => file_get_unused_draft_itemid()
    );
}

/**
 * 获取所有资源类型选项
 */
function local_dels_resourcecorner_get_resource_types() {
    return array(
        'file' => get_string('file', 'local_dels_resourcecorner'),
        'video' => get_string('video', 'local_dels_resourcecorner')
    );
}