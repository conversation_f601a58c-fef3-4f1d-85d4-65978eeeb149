<?xml version="1.0" encoding="UTF-8" ?>
<XMLDB PATH="local/dels_resourcecorner/db" VERSION="20240601" COMMENT="XMLDB file for Moodle local/dels_resourcecorner"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="../../../lib/xmldb/xmldb.xsd"
>
  <TABLES>
    <TABLE NAME="local_dels_resourcecorner" COMMENT="Stores resources for the Resource Corner plugin">
      <FIELDS>
        <FIELD NAME="id" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="true"/>
        <FIELD NAME="title" TYPE="char" LENGTH="255" NOTNULL="true" SEQUENCE="false" COMMENT="Title of the resource"/>
        <FIELD NAME="description" TYPE="text" NOTNULL="false" SEQUENCE="false" COMMENT="Description of the resource"/>
        <FIELD NAME="companyid" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false"/>
        <FIELD NAME="file_id" TYPE="int" LENGTH="10" NOTNULL="false" SEQUENCE="false" COMMENT="ID of the associated file in the Moodle files table"/>
        <FIELD NAME="resource_type" TYPE="char" LENGTH="10" NOTNULL="true" DEFAULT="file" SEQUENCE="false" COMMENT="Type of resource (file or video)"/>
        <FIELD NAME="allow_download" TYPE="int" LENGTH="1" NOTNULL="true" DEFAULT="0" SEQUENCE="false" COMMENT="Whether downloading is allowed (0/1)"/>
        <FIELD NAME="time_created" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Timestamp when the resource was created"/>
        <FIELD NAME="time_modified" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="Timestamp when the resource was last modified"/>
        <FIELD NAME="created_by" TYPE="int" LENGTH="10" NOTNULL="true" SEQUENCE="false" COMMENT="User ID of the creator"/>
      </FIELDS>
      <KEYS>
        <KEY NAME="primary" TYPE="primary" FIELDS="id"/>
        <KEY NAME="file_fk" TYPE="foreign" FIELDS="file_id" REFTABLE="files" REFFIELDS="id"/>
        <KEY NAME="created_by_fk" TYPE="foreign" FIELDS="created_by" REFTABLE="user" REFFIELDS="id"/>
      </KEYS>
      <INDEXES>
        <INDEX NAME="resource_type_idx" UNIQUE="false" FIELDS="resource_type"/>
        <INDEX NAME="allow_download_idx" UNIQUE="false" FIELDS="allow_download"/>
        <INDEX NAME="time_created_idx" UNIQUE="false" FIELDS="time_created"/>
      </INDEXES>
    </TABLE>
  </TABLES>
</XMLDB>