<!DOCTYPE html>
<html>

<head>
    <title>Login Form</title>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
</head>

<body>
    <form name="login-form" id="myForm" action="process_form.php" method="POST">
        <input type="text" name="username" placeholder="Username" required />
        <input type="password" name="password" placeholder="Password" required />

        <!-- Turnstile widget with basic configuration -->
        <div class="cf-turnstile" data-sitekey="0x4AAAAAAB0lFNocek4gFM2N" data-callback="onTurnstileSuccess" data-error-callback="onTurnstileError"></div>

        <button type="submit">Log in</button>
    </form>

    <script>// Callback when Turnstile is successfully solved
        function onTurnstileSuccess(token) {
            console.log('Turnstile solved:', token);
            document.getElementById('submitBtn').disabled = false;  // Enable submit
            // Optionally, store token in a hidden input if not auto-generated
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'cf-turnstile-response';
            hiddenInput.value = token;
            document.getElementById('myForm').appendChild(hiddenInput);
        }

        // Callback on error (e.g., unsolved or failed)
        function onTurnstileError() {
            console.log('Turnstile error');
            document.getElementById('submitBtn').disabled = true;  // Keep disabled
            alert('Please complete the verification.');
        }

        // Optional: Form submission handler to double-check
        document.getElementById('myForm').addEventListener('submit', function (e) {
            const token = document.querySelector('input[name="cf-turnstile-response"]')?.value;
            if (!token) {
                e.preventDefault();
                alert('Verification required.');
                return false;
            }
            // Proceed with submission (token will be sent to server)
        });
    </script>
</body>

</html>