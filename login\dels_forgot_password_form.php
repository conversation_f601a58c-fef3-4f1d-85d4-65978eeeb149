<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Custom forgot password form with Turnstile CAPTCHA.
 *
 * @package    core
 * @subpackage auth
 * @copyright  2024 DELS CPT
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
defined('MOODLE_INTERNAL') || die();

require_once($CFG->libdir.'/formslib.php');
require_once($CFG->dirroot.'/user/lib.php');
require_once('lib.php');

/**
 * Custom forgot password form with Turnstile CAPTCHA.
 */
class dels_forgot_password_form extends moodleform {

    /**
     * Define the forgot password form.
     */
    function definition() {
        global $USER, $PAGE;

        $mform = $this->_form;
        $mform->setDisableShortforms(true);

        // Add Turnstile JavaScript API
        $PAGE->requires->js(new moodle_url('https://challenges.cloudflare.com/turnstile/v0/api.js'), true);

        // Hook for plugins to extend form definition.
        core_login_extend_forgot_password_form($mform);

        $mform->addElement('header', 'searchbyusername', get_string('searchbyusername'), '');

        $purpose = user_edit_map_field_purpose($USER->id, 'username');
        $mform->addElement('text', 'username', get_string('username'), 'size="20"' . $purpose);
        $mform->setType('username', PARAM_RAW);

        $submitlabel = get_string('search');
        $mform->addElement('submit', 'submitbuttonusername', $submitlabel);

        $mform->addElement('header', 'searchbyemail', get_string('searchbyemail'), '');

        $purpose = user_edit_map_field_purpose($USER->id, 'email');
        $mform->addElement('text', 'email', get_string('email'), 'maxlength="100" size="30"' . $purpose);
        $mform->setType('email', PARAM_RAW_TRIMMED);

        $submitlabel = get_string('search');
        $mform->addElement('submit', 'submitbuttonemail', $submitlabel);

        // Add CAPTCHA section
        $mform->addElement('header', 'captcha_header', 'Security Verification');

        // Add Turnstile widget as HTML element
        $turnstile_html = html_writer::div('', 'cf-turnstile', [
            'data-sitekey' => '0x4AAAAAAB0lFNocek4gFM2N',
            'data-callback' => 'onTurnstileSuccess',
            'data-error-callback' => 'onTurnstileError'
        ]);

        $mform->addElement('html', $turnstile_html);

        // Add hidden field to store the response token
        $mform->addElement('hidden', 'cf-turnstile-response', '');
        $mform->setType('cf-turnstile-response', PARAM_RAW);

        // Add JavaScript for handling callbacks and form validation
        $js = "
        window.onTurnstileSuccess = function(token) {
            console.log('Turnstile solved:', token);
            var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
            if (responseField) {
                responseField.value = token;
            }
        };
        
        window.onTurnstileError = function() {
            console.log('Turnstile error');
            var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
            if (responseField) {
                responseField.value = '';
            }
            alert('Please complete the verification.');
        };
        
        // Add form validation to ensure CAPTCHA is completed before submission
        document.addEventListener('DOMContentLoaded', function() {
            var forms = document.querySelectorAll('form');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(e) {
                    var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
                    if (responseField && !responseField.value) {
                        e.preventDefault();
                        alert('Security verification is required.');
                        return false;
                    }
                });
            });
        });
        ";

        $PAGE->requires->js_init_code($js);

        // Add CSS styles
        $css = "
        .cf-turnstile {
            margin: 10px 0;
            display: flex;
            justify-content: center;
        }
        #fgroup_id_captcha_header {
            margin-top: 20px;
        }
        .form-group .error[data-fieldname=\"captcha_header\"] {
            color: #d32f2f;
            font-weight: bold;
            margin-top: 5px;
        }
        @media (max-width: 768px) {
            .cf-turnstile {
                transform: scale(0.8);
                transform-origin: center;
            }
        }
        ";

        $PAGE->requires->css_theme(new moodle_url('data:text/css;base64,' . base64_encode($css)));
    }

    /**
     * Validate user input from the forgot password form.
     * @param array $data array of submitted form fields.
     * @param array $files submitted with the form.
     * @return array errors occuring during validation.
     */
    function validation($data, $files) {

        $errors = parent::validation($data, $files);

        // Extend validation for any form extensions from plugins.
        $errors = array_merge($errors, core_login_validate_extend_forgot_password_form($data));

        // Validate CAPTCHA
        $captcha_errors = $this->validate_turnstile_captcha($data);
        $errors = array_merge($errors, $captcha_errors);

        $errors += core_login_validate_forgot_password_data($data);

        return $errors;
    }

    /**
     * Validate Turnstile CAPTCHA response.
     * @param array $data The form data
     * @return array Array of validation errors
     */
    private function validate_turnstile_captcha($data) {
        $errors = array();

        // Check if CAPTCHA response is present
        if (empty($data['cf-turnstile-response'])) {
            $errors['captcha_header'] = 'Security verification is required.';
            return $errors;
        }

        // Validate the CAPTCHA response with Cloudflare
        $token = $data['cf-turnstile-response'];
        $secret = '0x4AAAAAAB0lFNiF6L0Px1R62tsHd-GOJSo';
        $remoteip = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? 
                    $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
                    $_SERVER['REMOTE_ADDR'];

        $validation = $this->validate_turnstile_token($token, $secret, $remoteip);

        if (!$validation['success']) {
            $errors['captcha_header'] = 'Security verification failed. Please try again.';
            error_log('Turnstile validation failed: ' . implode(', ', $validation['error-codes'] ?? []));
        }

        return $errors;
    }

    /**
     * Validate Turnstile CAPTCHA token with Cloudflare API.
     * @param string $token The CAPTCHA response token
     * @param string $secret The secret key
     * @param string|null $remoteip The user's IP address
     * @return array The validation response
     */
    private function validate_turnstile_token($token, $secret, $remoteip = null) {
        $url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';

        $data = [
            'secret' => $secret,
            'response' => $token
        ];

        if ($remoteip) {
            $data['remoteip'] = $remoteip;
        }

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];

        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);

        if ($response === FALSE) {
            return ['success' => false, 'error-codes' => ['internal-error']];
        }

        return json_decode($response, true);
    }
}
