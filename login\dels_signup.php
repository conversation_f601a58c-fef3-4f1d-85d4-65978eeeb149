<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Custom user signup page for Child Protection Training with Turnstile CAPTCHA.
 *
 * @package    core
 * @subpackage auth
 * @copyright  2024 DELS CPT
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require('../config.php');
require_once($CFG->dirroot . '/user/editlib.php');
require_once($CFG->libdir . '/authlib.php');
require_once($CFG->dirroot . '/local/iomad/lib/company.php');
require_once('lib.php');
require_once('dels_signup_form.php');

$userdecision = optional_param('userdecision', false, PARAM_BOOL);
$userclashed = optional_param('userclashed', false, PARAM_BOOL);
$wanteddepartment = optional_param('dept', '', PARAM_CLEAN);

if (!$authplugin = signup_is_enabled()) {
    throw new \moodle_exception('notlocalisederrormessage', 'error', '', 'Sorry, you may not use this page.');
}

$PAGE->set_url('/login/dels_signup.php');
$PAGE->set_context(context_system::instance());

// IOMAD - Set up Child Protection Training company automatically
$cpt_company = $DB->get_record('company', ['shortname' => 'CPT']);
if (!$cpt_company) {
    // Show a user-friendly error page
    $PAGE->set_pagelayout('login');
    $PAGE->set_title('Company Setup Required');
    $PAGE->set_heading($SITE->fullname);

    echo $OUTPUT->header();
    echo $OUTPUT->box('<h3>Company Setup Required</h3>
    <p>The "Child Protection Training" company (shortname: CPT) has not been set up yet.</p>
    <p><strong>Administrator:</strong> Please create a company with the shortname "CPT" in the IOMAD Company Administration before users can register.</p>
    <p><a href="' . $CFG->wwwroot . '/login/index.php">Return to Login</a></p>',
    'generalbox boxwidthnormal boxaligncenter');
    echo $OUTPUT->footer();
    die;
}

// Set the company in session
$SESSION->company = $cpt_company;
$wantedcompanyid = $cpt_company->id;
$wantedcompanyshort = $cpt_company->shortname;

// Did we get redirected here due to a user clash?
if (!empty($SESSION->signupuserinothercompany) || $userclashed) {
    // Take away the stored setting.
    unset($SESSION->signupuserinothercompany);

    // What does the user want to do now?
    if (empty($userdecision)) {
        // Find out what they want to do.
        $optionsyes = ['userdecision' => true,
                       'userclashed' => true];
        echo $OUTPUT->header();
        echo $OUTPUT->heading(get_string('usercompanyclashed', 'block_iomad_company_admin'), 2, 'headingblock header');
        echo $OUTPUT->confirm(get_string('usercompanyclashedfull', 'block_iomad_company_admin'),
                              new moodle_url('/login/dels_signup.php', $optionsyes), '/login/dels_signup.php');
        echo $OUTPUT->footer();
        die;
    } else {
        // User decided to add this account to the current company.
        $company = new company($wantedcompanyid);
        $company->assign_user_to_company($SESSION->clasheduserid);
        unset($SESSION->clasheduserid);
        redirect(new moodle_url('/login/index.php'),
                 get_string('userassignedtocompanyclashed', 'block_iomad_company_admin'),
                 null,
                 \core\output\notification::NOTIFY_SUCCESS);
        die;
    }
}

// If wantsurl is empty or /login/dels_signup.php, override wanted URL.
// We do not want to end up here again if user clicks "Login".
if (empty($SESSION->wantsurl)) {
    $SESSION->wantsurl = $CFG->wwwroot . '/';
} else {
    $wantsurl = new moodle_url($SESSION->wantsurl);
    if ($PAGE->url->compare($wantsurl, URL_MATCH_BASE)) {
        $SESSION->wantsurl = $CFG->wwwroot . '/';
    }
}

// Set up the company context for IOMAD
$SESSION->currenteditingcompany = $cpt_company->id;

// Try to set company context, fall back to system context if it fails
try {
    $companycontext = \core\context\company::instance($cpt_company->id);
    $PAGE->set_context($companycontext);
} catch (Exception $e) {
    // Fall back to system context if company context fails
    $PAGE->set_context(context_system::instance());
}

// Plugins can create pre sign up requests.
// Can be used to force additional actions before sign up such as acceptance of policies, validations, etc.
core_login_pre_signup_requests();

$mform_signup = new dels_signup_form();

if ($mform_signup->is_cancelled()) {
    // IOMAD - We want the company/theme to persist.
    $redirect = get_login_url();
    if (!empty($SESSION->company)) {
        $redirect .= "?id=" . $SESSION->company->id . "&code=" . $SESSION->company->shortname;
    }

    redirect($redirect);

} else if ($user = $mform_signup->get_data()) {
    // We don't use email as username for CPT - username is separate

    // Set up defaults for user from company defaults
    if (!empty($SESSION->company)) {
        if (empty($user->city) && !empty($cpt_company->city)) {
            $user->city = $cpt_company->city;
        }
        if (empty($user->country) && !empty($cpt_company->country)) {
            $user->country = $cpt_company->country;
        }
    }

    // Add missing required fields.
    $user = signup_setup_new_user($user);

    // Plugins can perform post sign up actions once data has been validated.
    core_login_post_signup_requests($user);

    $authplugin->user_signup($user, true); // prints notice and link to login/index.php
    exit; //never reached
}

$newaccount = get_string('newaccount');
$login      = get_string('login');

$PAGE->navbar->add($login);
$PAGE->navbar->add($newaccount);

$PAGE->set_pagelayout('login');
$PAGE->set_title($newaccount);
$PAGE->set_heading($SITE->fullname);

echo $OUTPUT->header();

// Add information about Child Protection Training
echo $OUTPUT->box('<h3>Child Protection Training Registration</h3>
<p>Welcome to the Child Protection Training registration page. Please fill out the form below to create your account.</p>', 
'generalbox boxwidthnormal boxaligncenter');

if ($mform_signup instanceof renderable) {
    // Try and use the renderer from the auth plugin if it exists.
    try {
        $renderer = $PAGE->get_renderer('auth_' . $authplugin->authtype);
    } catch (coding_exception $ce) {
        // Fall back on the general renderer.
        $renderer = $OUTPUT;
    }
    echo $renderer->render($mform_signup);
} else {
    // Fall back for auth plugins not using renderables.
    $mform_signup->display();
}
echo $OUTPUT->footer();
