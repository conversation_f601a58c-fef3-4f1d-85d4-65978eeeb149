<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Library functions for the Turnstile CAPTCHA local plugin.
 *
 * @package    local_turnstile_captcha
 * @copyright  2024 DELS CPT
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Extend the forgot password form with Turnstile CAPTCHA.
 *
 * This function is called by the core_login_extend_forgot_password_form hook.
 *
 * @param moodleform $mform The form to extend
 */
function extend_forgot_password_form($mform) {
    global $PAGE;

    // Debug logging
    error_log('Turnstile CAPTCHA: extend_forgot_password_form called');

    // Add Turnstile JavaScript API
    $PAGE->requires->js(new moodle_url('https://challenges.cloudflare.com/turnstile/v0/api.js'), true);

    // Add CSS styles
    $PAGE->requires->css('/local/turnstile_captcha/styles.css');
    
    // Add CAPTCHA section header
    $mform->addElement('header', 'captcha_header', get_string('captcha', 'local_turnstile_captcha'));
    
    // Add Turnstile widget as HTML element
    $turnstile_html = html_writer::div('', 'cf-turnstile', [
        'data-sitekey' => '0x4AAAAAAB0lFNocek4gFM2N',
        'data-callback' => 'onTurnstileSuccess',
        'data-error-callback' => 'onTurnstileError'
    ]);
    
    $mform->addElement('html', $turnstile_html);
    
    // Add hidden field to store the response token
    $mform->addElement('hidden', 'cf-turnstile-response', '');
    $mform->setType('cf-turnstile-response', PARAM_RAW);
    
    // Add JavaScript for handling callbacks and form validation
    $js = "
    window.onTurnstileSuccess = function(token) {
        console.log('Turnstile solved:', token);
        var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
        if (responseField) {
            responseField.value = token;
        }
    };

    window.onTurnstileError = function() {
        console.log('Turnstile error');
        var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
        if (responseField) {
            responseField.value = '';
        }
        alert('" . get_string('captcha_error', 'local_turnstile_captcha') . "');
    };

    // Add form validation to ensure CAPTCHA is completed before submission
    document.addEventListener('DOMContentLoaded', function() {
        var forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                var responseField = document.querySelector('input[name=\"cf-turnstile-response\"]');
                if (responseField && !responseField.value) {
                    e.preventDefault();
                    alert('" . get_string('captcha_required', 'local_turnstile_captcha') . "');
                    return false;
                }
            });
        });
    });
    ";

    $PAGE->requires->js_init_code($js);
}

/**
 * Validate the Turnstile CAPTCHA response.
 *
 * This function is called by the core_login_validate_extend_forgot_password_form hook.
 *
 * @param array $data The form data
 * @return array Array of validation errors
 */
function validate_extend_forgot_password_form($data) {
    $errors = array();

    // Debug logging
    error_log('Turnstile CAPTCHA: validate_extend_forgot_password_form called');

    // Check if CAPTCHA response is present
    if (empty($data['cf-turnstile-response'])) {
        $errors['captcha_header'] = get_string('captcha_required', 'local_turnstile_captcha');
        return $errors;
    }
    
    // Validate the CAPTCHA response with Cloudflare
    $token = $data['cf-turnstile-response'];
    $secret = '0x4AAAAAAB0lFNiF6L0Px1R62tsHd-GOJSo';
    $remoteip = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? 
                $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
                $_SERVER['REMOTE_ADDR'];
    
    $validation = validate_turnstile($token, $secret, $remoteip);
    
    if (!$validation['success']) {
        $errors['captcha_header'] = get_string('captcha_invalid', 'local_turnstile_captcha');
        error_log('Turnstile validation failed: ' . implode(', ', $validation['error-codes'] ?? []));
    }
    
    return $errors;
}

/**
 * Validate Turnstile CAPTCHA token with Cloudflare API.
 *
 * @param string $token The CAPTCHA response token
 * @param string $secret The secret key
 * @param string|null $remoteip The user's IP address
 * @return array The validation response
 */
function validate_turnstile($token, $secret, $remoteip = null) {
    $url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify';
    
    $data = [
        'secret' => $secret,
        'response' => $token
    ];
    
    if ($remoteip) {
        $data['remoteip'] = $remoteip;
    }
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $response = file_get_contents($url, false, $context);
    
    if ($response === FALSE) {
        return ['success' => false, 'error-codes' => ['internal-error']];
    }
    
    return json_decode($response, true);
}
