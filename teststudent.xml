<?xml version="1.0" encoding="UTF-8"?>
<role><shortname>teststudent</shortname><name>test student 1</name><description>test student test student</description><archetype>student</archetype><contextlevels><level>course</level><level>module</level></contextlevels><allowassign/><allowoverride/><allowswitch/><allowview><shortname>coursecreator</shortname><shortname>editingteacher</shortname><shortname>teacher</shortname><shortname>student</shortname></allowview><permissions><inherit>atto/h5p:addembed</inherit><inherit>atto/recordrtc:recordaudio</inherit><inherit>atto/recordrtc:recordvideo</inherit><inherit>auth/iomadoidc:manageconnection</inherit><inherit>auth/iomadoidc:manageconnectionconnect</inherit><inherit>auth/iomadoidc:manageconnectiondisconnect</inherit><inherit>auth/oauth2:managelinkedlogins</inherit><inherit>block/accessreview:addinstance</inherit><inherit>block/accessreview:view</inherit><inherit>block/activity_modules:addinstance</inherit><inherit>block/activity_results:addinstance</inherit><inherit>block/admin_bookmarks:addinstance</inherit><inherit>block/admin_bookmarks:myaddinstance</inherit><inherit>block/badges:addinstance</inherit><inherit>block/badges:myaddinstance</inherit><inherit>block/blog_menu:addinstance</inherit><inherit>block/blog_recent:addinstance</inherit><inherit>block/blog_tags:addinstance</inherit><inherit>block/calendar_month:addinstance</inherit><inherit>block/calendar_month:myaddinstance</inherit><inherit>block/calendar_upcoming:addinstance</inherit><inherit>block/calendar_upcoming:myaddinstance</inherit><inherit>block/comments:addinstance</inherit><inherit>block/comments:myaddinstance</inherit><inherit>block/completionstatus:addinstance</inherit><inherit>block/course_list:addinstance</inherit><inherit>block/course_list:myaddinstance</inherit><inherit>block/course_summary:addinstance</inherit><inherit>block/feedback:addinstance</inherit><inherit>block/globalsearch:addinstance</inherit><inherit>block/globalsearch:myaddinstance</inherit><inherit>block/glossary_random:addinstance</inherit><inherit>block/glossary_random:myaddinstance</inherit><inherit>block/html:addinstance</inherit><inherit>block/html:myaddinstance</inherit><inherit>block/iomad_approve_access:addinstance</inherit><inherit>block/iomad_approve_access:approve</inherit><inherit>block/iomad_approve_access:myaddinstance</inherit><inherit>block/iomad_commerce:add_course</inherit><inherit>block/iomad_commerce:addinstance</inherit><inherit>block/iomad_commerce:admin_view</inherit><inherit>block/iomad_commerce:buyinbulk</inherit><inherit>block/iomad_commerce:buyitnow</inherit><inherit>block/iomad_commerce:delete_course</inherit><inherit>block/iomad_commerce:edit_course</inherit><inherit>block/iomad_commerce:hide_course</inherit><inherit>block/iomad_commerce:manage_default</inherit><inherit>block/iomad_commerce:manage_tags</inherit><inherit>block/iomad_commerce:myaddinstance</inherit><inherit>block/iomad_company_admin:addinstance</inherit><inherit>block/iomad_company_admin:allcompany_user_profiles</inherit><inherit>block/iomad_company_admin:allocate_licenses</inherit><inherit>block/iomad_company_admin:assign_company_manager</inherit><inherit>block/iomad_company_admin:assign_company_reporter</inherit><inherit>block/iomad_company_admin:assign_department_manager</inherit><inherit>block/iomad_company_admin:assign_educator</inherit><inherit>block/iomad_company_admin:assign_groups</inherit><inherit>block/iomad_company_admin:canviewchildren</inherit><inherit>block/iomad_company_admin:classrooms</inherit><inherit>block/iomad_company_admin:classrooms_add</inherit><inherit>block/iomad_company_admin:classrooms_delete</inherit><inherit>block/iomad_company_admin:classrooms_edit</inherit><inherit>block/iomad_company_admin:company_add</inherit><inherit>block/iomad_company_admin:company_add_child</inherit><inherit>block/iomad_company_admin:company_course</inherit><inherit>block/iomad_company_admin:company_course_unenrol</inherit><inherit>block/iomad_company_admin:company_course_users</inherit><inherit>block/iomad_company_admin:company_delete</inherit><inherit>block/iomad_company_admin:company_edit</inherit><inherit>block/iomad_company_admin:company_edit_appearance</inherit><inherit>block/iomad_company_admin:company_edit_certificateinfo</inherit><inherit>block/iomad_company_admin:company_edit_restricted</inherit><inherit>block/iomad_company_admin:company_edit_smtp</inherit><inherit>block/iomad_company_admin:company_framework</inherit><inherit>block/iomad_company_admin:company_license_users</inherit><inherit>block/iomad_company_admin:company_manager</inherit><inherit>block/iomad_company_admin:company_template</inherit><inherit>block/iomad_company_admin:company_user</inherit><inherit>block/iomad_company_admin:company_user_profiles</inherit><inherit>block/iomad_company_admin:company_view</inherit><inherit>block/iomad_company_admin:company_view_all</inherit><inherit>block/iomad_company_admin:companyadvancedsettings</inherit><inherit>block/iomad_company_admin:companymanagement_view</inherit><inherit>block/iomad_company_admin:competencymanagement_view</inherit><inherit>block/iomad_company_admin:competencyview</inherit><inherit>block/iomad_company_admin:configiomadoidc</inherit><inherit>block/iomad_company_admin:configiomadoidcsync</inherit><inherit>block/iomad_company_admin:configiomadsaml2</inherit><inherit>block/iomad_company_admin:configmfa</inherit><inherit>block/iomad_company_admin:configpolicies</inherit><inherit>block/iomad_company_admin:coursemanagement_view</inherit><inherit>block/iomad_company_admin:createcourse</inherit><inherit>block/iomad_company_admin:delegatecourse</inherit><inherit>block/iomad_company_admin:deleteallcourses</inherit><inherit>block/iomad_company_admin:deletecourses</inherit><inherit>block/iomad_company_admin:deleteuser</inherit><inherit>block/iomad_company_admin:destroycourses</inherit><inherit>block/iomad_company_admin:downloadcertificates</inherit><inherit>block/iomad_company_admin:downloadmycertificates</inherit><inherit>block/iomad_company_admin:edit_all_departments</inherit><inherit>block/iomad_company_admin:edit_departments</inherit><inherit>block/iomad_company_admin:edit_groups</inherit><inherit>block/iomad_company_admin:edit_licenses</inherit><inherit>block/iomad_company_admin:edit_my_licenses</inherit><inherit>block/iomad_company_admin:editallusers</inherit><inherit>block/iomad_company_admin:editmanagers</inherit><inherit>block/iomad_company_admin:editpubliclocation</inherit><inherit>block/iomad_company_admin:edituserpassword</inherit><inherit>block/iomad_company_admin:editusers</inherit><inherit>block/iomad_company_admin:export_departments</inherit><inherit>block/iomad_company_admin:hideshowallcourses</inherit><inherit>block/iomad_company_admin:hideshowcourses</inherit><inherit>block/iomad_company_admin:import_departments</inherit><inherit>block/iomad_company_admin:licensemanagement_view</inherit><inherit>block/iomad_company_admin:manageallcourses</inherit><inherit>block/iomad_company_admin:managecourses</inherit><inherit>block/iomad_company_admin:manageframeworks</inherit><inherit>block/iomad_company_admin:managetemplates</inherit><inherit>block/iomad_company_admin:myaddinstance</inherit><inherit>block/iomad_company_admin:restrict_capabilities</inherit><inherit>block/iomad_company_admin:split_my_licenses</inherit><inherit>block/iomad_company_admin:suspendcompanies</inherit><inherit>block/iomad_company_admin:suspenduser</inherit><inherit>block/iomad_company_admin:templateview</inherit><inherit>block/iomad_company_admin:unallocate_licenses</inherit><inherit>block/iomad_company_admin:user_create</inherit><inherit>block/iomad_company_admin:user_upload</inherit><inherit>block/iomad_company_admin:usermanagement_view</inherit><inherit>block/iomad_company_admin:view_editusers</inherit><inherit>block/iomad_company_admin:view_licenses</inherit><inherit>block/iomad_company_admin:view_my_company_email</inherit><inherit>block/iomad_company_admin:viewallsharedcourses</inherit><inherit>block/iomad_company_admin:viewcourses</inherit><inherit>block/iomad_company_admin:viewsuspendedusers</inherit><inherit>block/iomad_company_selector:addinstance</inherit><inherit>block/iomad_company_selector:myaddinstance</inherit><inherit>block/iomad_html:addinstance</inherit><inherit>block/iomad_html:myaddinstance</inherit><inherit>block/iomad_learningpath:addinstance</inherit><inherit>block/iomad_learningpath:myaddinstance</inherit><inherit>block/iomad_link:addinstance</inherit><inherit>block/iomad_link:myaddinstance</inherit><inherit>block/iomad_link:view</inherit><inherit>block/iomad_microlearning:addinstance</inherit><inherit>block/iomad_microlearning:assign_threads</inherit><inherit>block/iomad_microlearning:edit_nuggets</inherit><inherit>block/iomad_microlearning:edit_threads</inherit><inherit>block/iomad_microlearning:import_threads</inherit><inherit>block/iomad_microlearning:importgroupfromcsv</inherit><inherit>block/iomad_microlearning:manage_groups</inherit><inherit>block/iomad_microlearning:myaddinstance</inherit><inherit>block/iomad_microlearning:thread_clone</inherit><inherit>block/iomad_microlearning:thread_delete</inherit><inherit>block/iomad_microlearning:thread_view</inherit><inherit>block/iomad_microlearning:view</inherit><inherit>block/iomad_onlineusers:addinstance</inherit><inherit>block/iomad_onlineusers:myaddinstance</inherit><inherit>block/iomad_reports:addinstance</inherit><inherit>block/iomad_reports:myaddinstance</inherit><inherit>block/iomad_reports:view</inherit><inherit>block/iomad_welcome:addinstance</inherit><inherit>block/iomad_welcome:myaddinstance</inherit><inherit>block/iomad_welcome:view</inherit><inherit>block/login:addinstance</inherit><inherit>block/lp:addinstance</inherit><inherit>block/lp:myaddinstance</inherit><inherit>block/mentees:addinstance</inherit><inherit>block/mentees:myaddinstance</inherit><inherit>block/mnet_hosts:addinstance</inherit><inherit>block/mnet_hosts:myaddinstance</inherit><inherit>block/mycourses:addinstance</inherit><inherit>block/mycourses:myaddinstance</inherit><inherit>block/myoverview:myaddinstance</inherit><inherit>block/myprofile:addinstance</inherit><inherit>block/myprofile:myaddinstance</inherit><inherit>block/navigation:addinstance</inherit><inherit>block/navigation:myaddinstance</inherit><inherit>block/news_items:addinstance</inherit><inherit>block/news_items:myaddinstance</inherit><inherit>block/online_users:addinstance</inherit><inherit>block/online_users:myaddinstance</inherit><inherit>block/private_files:addinstance</inherit><inherit>block/private_files:myaddinstance</inherit><inherit>block/recent_activity:addinstance</inherit><inherit>block/recent_activity:viewaddupdatemodule</inherit><inherit>block/recent_activity:viewdeletemodule</inherit><inherit>block/recentlyaccessedcourses:myaddinstance</inherit><inherit>block/recentlyaccesseditems:myaddinstance</inherit><inherit>block/rss_client:addinstance</inherit><inherit>block/rss_client:manageanyfeeds</inherit><inherit>block/rss_client:manageownfeeds</inherit><inherit>block/rss_client:myaddinstance</inherit><inherit>block/search_forums:addinstance</inherit><inherit>block/section_links:addinstance</inherit><inherit>block/selfcompletion:addinstance</inherit><inherit>block/settings:addinstance</inherit><inherit>block/settings:myaddinstance</inherit><inherit>block/site_main_menu:addinstance</inherit><inherit>block/social_activities:addinstance</inherit><inherit>block/starredcourses:myaddinstance</inherit><inherit>block/tag_flickr:addinstance</inherit><inherit>block/tag_youtube:addinstance</inherit><inherit>block/tags:addinstance</inherit><inherit>block/tags:myaddinstance</inherit><inherit>block/timeline:myaddinstance</inherit><inherit>booktool/exportimscp:export</inherit><inherit>booktool/importhtml:import</inherit><inherit>communication/matrix:moderator</inherit><inherit>contenttype/h5p:access</inherit><inherit>contenttype/h5p:upload</inherit><inherit>contenttype/h5p:useeditor</inherit><inherit>enrol/category:config</inherit><inherit>enrol/category:synchronised</inherit><inherit>enrol/cohort:config</inherit><inherit>enrol/cohort:unenrol</inherit><inherit>enrol/database:config</inherit><inherit>enrol/database:unenrol</inherit><inherit>enrol/fee:config</inherit><inherit>enrol/fee:manage</inherit><inherit>enrol/fee:unenrol</inherit><inherit>enrol/fee:unenrolself</inherit><inherit>enrol/flatfile:manage</inherit><inherit>enrol/flatfile:unenrol</inherit><inherit>enrol/guest:config</inherit><inherit>enrol/imsenterprise:config</inherit><inherit>enrol/ldap:manage</inherit><inherit>enrol/license:config</inherit><inherit>enrol/license:manage</inherit><inherit>enrol/license:unenrol</inherit><inherit>enrol/lti:config</inherit><inherit>enrol/lti:unenrol</inherit><inherit>enrol/manual:config</inherit><inherit>enrol/manual:enrol</inherit><inherit>enrol/manual:manage</inherit><inherit>enrol/manual:unenrol</inherit><inherit>enrol/manual:unenrolself</inherit><inherit>enrol/meta:config</inherit><inherit>enrol/meta:selectaslinked</inherit><inherit>enrol/meta:unenrol</inherit><inherit>enrol/mnet:config</inherit><inherit>enrol/paypal:config</inherit><inherit>enrol/paypal:manage</inherit><inherit>enrol/paypal:unenrol</inherit><inherit>enrol/paypal:unenrolself</inherit><inherit>enrol/self:config</inherit><inherit>enrol/self:enrolself</inherit><inherit>enrol/self:holdkey</inherit><inherit>enrol/self:manage</inherit><inherit>enrol/self:unenrol</inherit><inherit>factor/capability:cannotpassfactor</inherit><inherit>forumreport/summary:view</inherit><inherit>forumreport/summary:viewall</inherit><inherit>gradeexport/ods:publish</inherit><inherit>gradeexport/ods:view</inherit><inherit>gradeexport/txt:publish</inherit><inherit>gradeexport/txt:view</inherit><inherit>gradeexport/xls:publish</inherit><inherit>gradeexport/xls:view</inherit><inherit>gradeexport/xml:publish</inherit><inherit>gradeexport/xml:view</inherit><inherit>gradeimport/csv:view</inherit><inherit>gradeimport/direct:view</inherit><inherit>gradeimport/xml:publish</inherit><inherit>gradeimport/xml:view</inherit><inherit>gradereport/grader:view</inherit><inherit>gradereport/history:view</inherit><inherit>gradereport/outcomes:view</inherit><inherit>gradereport/overview:view</inherit><inherit>gradereport/singleview:view</inherit><inherit>gradereport/summary:view</inherit><inherit>local/dels_resourcecorner:manage</inherit><inherit>local/dels_resourcecorner:view</inherit><inherit>local/email:add</inherit><inherit>local/email:delete</inherit><inherit>local/email:edit</inherit><inherit>local/email:list</inherit><inherit>local/email:send</inherit><inherit>local/email:templateset_list</inherit><inherit>local/iomad_learningpath:manage</inherit><inherit>local/iomad_learningpath:view</inherit><inherit>local/iomad_oidc_sync:manage</inherit><inherit>local/iomad_oidc_sync:view</inherit><inherit>local/iomad_settings:addinstance</inherit><inherit>local/iomad_track:importfrommoodle</inherit><inherit>local/iomadcustompage:edit</inherit><inherit>local/iomadcustompage:editall</inherit><inherit>local/iomadcustompage:view</inherit><inherit>local/report_attendance:view</inherit><inherit>local/report_companies:view</inherit><inherit>local/report_completion_monthly:view</inherit><inherit>local/report_completion_overview:view</inherit><inherit>local/report_completion:view</inherit><inherit>local/report_emails:resend</inherit><inherit>local/report_emails:view</inherit><inherit>local/report_license_usage:view</inherit><inherit>local/report_user_license_allocations:view</inherit><inherit>local/report_user_logins:view</inherit><inherit>local/report_users:addentry</inherit><inherit>local/report_users:clearentries</inherit><inherit>local/report_users:deleteentries</inherit><inherit>local/report_users:deleteentriesfull</inherit><inherit>local/report_users:redocertificates</inherit><inherit>local/report_users:updateentries</inherit><inherit>local/report_users:view</inherit><inherit>message/airnotifier:managedevice</inherit><inherit>mod/assign:addinstance</inherit><inherit>mod/assign:editothersubmission</inherit><inherit>mod/assign:grade</inherit><inherit>mod/assign:grantextension</inherit><inherit>mod/assign:manageallocations</inherit><inherit>mod/assign:managegrades</inherit><inherit>mod/assign:manageoverrides</inherit><inherit>mod/assign:receivegradernotifications</inherit><inherit>mod/assign:releasegrades</inherit><inherit>mod/assign:revealidentities</inherit><inherit>mod/assign:reviewgrades</inherit><inherit>mod/assign:showhiddengrader</inherit><inherit>mod/assign:viewblinddetails</inherit><inherit>mod/assign:viewgrades</inherit><inherit>mod/bigbluebuttonbn:addinstance</inherit><inherit>mod/bigbluebuttonbn:addinstancewithmeeting</inherit><inherit>mod/bigbluebuttonbn:addinstancewithrecording</inherit><inherit>mod/bigbluebuttonbn:deleterecordings</inherit><inherit>mod/bigbluebuttonbn:importrecordings</inherit><inherit>mod/bigbluebuttonbn:managerecordings</inherit><inherit>mod/bigbluebuttonbn:protectrecordings</inherit><inherit>mod/bigbluebuttonbn:publishrecordings</inherit><inherit>mod/bigbluebuttonbn:seepresentation</inherit><inherit>mod/bigbluebuttonbn:unprotectrecordings</inherit><inherit>mod/bigbluebuttonbn:unpublishrecordings</inherit><inherit>mod/bigbluebuttonbn:viewallrecordingformats</inherit><inherit>mod/book:addinstance</inherit><inherit>mod/book:edit</inherit><inherit>mod/book:viewhiddenchapters</inherit><inherit>mod/chat:addinstance</inherit><inherit>mod/chat:deletelog</inherit><inherit>mod/chat:exportparticipatedsession</inherit><inherit>mod/chat:exportsession</inherit><inherit>mod/chat:view</inherit><inherit>mod/choice:addinstance</inherit><inherit>mod/choice:deleteresponses</inherit><inherit>mod/choice:downloadresponses</inherit><inherit>mod/choice:readresponses</inherit><inherit>mod/choice:view</inherit><inherit>mod/data:addinstance</inherit><inherit>mod/data:approve</inherit><inherit>mod/data:exportallentries</inherit><inherit>mod/data:exportentry</inherit><inherit>mod/data:exportuserinfo</inherit><inherit>mod/data:managecomments</inherit><inherit>mod/data:manageentries</inherit><inherit>mod/data:managetemplates</inherit><inherit>mod/data:manageuserpresets</inherit><inherit>mod/data:rate</inherit><inherit>mod/data:viewallratings</inherit><inherit>mod/data:viewalluserpresets</inherit><inherit>mod/data:viewanyrating</inherit><inherit>mod/data:viewrating</inherit><inherit>mod/feedback:addinstance</inherit><inherit>mod/feedback:createprivatetemplate</inherit><inherit>mod/feedback:createpublictemplate</inherit><inherit>mod/feedback:deletesubmissions</inherit><inherit>mod/feedback:deletetemplate</inherit><inherit>mod/feedback:edititems</inherit><inherit>mod/feedback:mapcourse</inherit><inherit>mod/feedback:receivemail</inherit><inherit>mod/feedback:viewreports</inherit><inherit>mod/folder:addinstance</inherit><inherit>mod/folder:managefiles</inherit><inherit>mod/folder:view</inherit><inherit>mod/forum:addinstance</inherit><inherit>mod/forum:addnews</inherit><inherit>mod/forum:addquestion</inherit><inherit>mod/forum:canoverridecutoff</inherit><inherit>mod/forum:canoverridediscussionlock</inherit><inherit>mod/forum:canposttomygroups</inherit><inherit>mod/forum:cantogglefavourite</inherit><inherit>mod/forum:deleteanypost</inherit><inherit>mod/forum:editanypost</inherit><inherit>mod/forum:exportdiscussion</inherit><inherit>mod/forum:exportforum</inherit><inherit>mod/forum:exportpost</inherit><inherit>mod/forum:grade</inherit><inherit>mod/forum:managesubscriptions</inherit><inherit>mod/forum:movediscussions</inherit><inherit>mod/forum:pindiscussions</inherit><inherit>mod/forum:postprivatereply</inherit><inherit>mod/forum:postwithoutthrottling</inherit><inherit>mod/forum:rate</inherit><inherit>mod/forum:readprivatereplies</inherit><inherit>mod/forum:replynews</inherit><inherit>mod/forum:splitdiscussions</inherit><inherit>mod/forum:viewallratings</inherit><inherit>mod/forum:viewanyrating</inherit><inherit>mod/forum:viewhiddentimedposts</inherit><inherit>mod/forum:viewqandawithoutposting</inherit><inherit>mod/forum:viewsubscribers</inherit><inherit>mod/glossary:addinstance</inherit><inherit>mod/glossary:approve</inherit><inherit>mod/glossary:export</inherit><inherit>mod/glossary:exportentry</inherit><inherit>mod/glossary:import</inherit><inherit>mod/glossary:managecategories</inherit><inherit>mod/glossary:managecomments</inherit><inherit>mod/glossary:manageentries</inherit><inherit>mod/glossary:rate</inherit><inherit>mod/glossary:viewallratings</inherit><inherit>mod/glossary:viewanyrating</inherit><inherit>mod/glossary:viewrating</inherit><inherit>mod/h5pactivity:addinstance</inherit><inherit>mod/h5pactivity:reviewattempts</inherit><inherit>mod/hvp:addinstance</inherit><inherit>mod/hvp:contenthubregistration</inherit><inherit>mod/hvp:emailconfirmsubmission</inherit><inherit>mod/hvp:emailnotifysubmission</inherit><inherit>mod/hvp:getembedcode</inherit><inherit>mod/hvp:getexport</inherit><inherit>mod/hvp:installrecommendedh5plibraries</inherit><inherit>mod/hvp:manage</inherit><inherit>mod/hvp:restrictlibraries</inherit><inherit>mod/hvp:share</inherit><inherit>mod/hvp:updatelibraries</inherit><inherit>mod/hvp:userestrictedlibraries</inherit><inherit>mod/hvp:viewallresults</inherit><inherit>mod/imscp:addinstance</inherit><inherit>mod/imscp:view</inherit><inherit>mod/iomadcertificate:addinstance</inherit><inherit>mod/iomadcertificate:manage</inherit><inherit>mod/iomadcertificate:printteacher</inherit><inherit>mod/iomadcertificate:viewother</inherit><inherit>mod/label:addinstance</inherit><inherit>mod/label:view</inherit><inherit>mod/lesson:addinstance</inherit><inherit>mod/lesson:edit</inherit><inherit>mod/lesson:grade</inherit><inherit>mod/lesson:manage</inherit><inherit>mod/lesson:manageoverrides</inherit><inherit>mod/lesson:view</inherit><inherit>mod/lesson:viewreports</inherit><inherit>mod/lti:addcoursetool</inherit><inherit>mod/lti:addinstance</inherit><inherit>mod/lti:addpreconfiguredinstance</inherit><inherit>mod/lti:admin</inherit><inherit>mod/lti:manage</inherit><inherit>mod/lti:requesttooladd</inherit><inherit>mod/page:addinstance</inherit><inherit>mod/page:view</inherit><inherit>mod/quiz:addinstance</inherit><inherit>mod/quiz:deleteattempts</inherit><inherit>mod/quiz:emailconfirmsubmission</inherit><inherit>mod/quiz:emailnotifyattemptgraded</inherit><inherit>mod/quiz:emailnotifysubmission</inherit><inherit>mod/quiz:emailwarnoverdue</inherit><inherit>mod/quiz:grade</inherit><inherit>mod/quiz:ignoretimelimits</inherit><inherit>mod/quiz:manage</inherit><inherit>mod/quiz:manageoverrides</inherit><inherit>mod/quiz:preview</inherit><inherit>mod/quiz:regrade</inherit><inherit>mod/quiz:reopenattempts</inherit><inherit>mod/quiz:viewoverrides</inherit><inherit>mod/quiz:viewreports</inherit><inherit>mod/resource:addinstance</inherit><inherit>mod/resource:view</inherit><inherit>mod/scorm:addinstance</inherit><inherit>mod/scorm:deleteownresponses</inherit><inherit>mod/scorm:deleteresponses</inherit><inherit>mod/scorm:viewreport</inherit><inherit>mod/subsection:addinstance</inherit><inherit>mod/survey:addinstance</inherit><inherit>mod/survey:download</inherit><inherit>mod/survey:readresponses</inherit><inherit>mod/trainingevent:add</inherit><inherit>mod/trainingevent:addinstance</inherit><inherit>mod/trainingevent:addoverride</inherit><inherit>mod/trainingevent:grade</inherit><inherit>mod/trainingevent:invite</inherit><inherit>mod/trainingevent:resetattendees</inherit><inherit>mod/trainingevent:viewallattendees</inherit><inherit>mod/trainingevent:viewattendees</inherit><inherit>mod/url:addinstance</inherit><inherit>mod/url:view</inherit><inherit>mod/wiki:addinstance</inherit><inherit>mod/wiki:managecomment</inherit><inherit>mod/wiki:managefiles</inherit><inherit>mod/wiki:managewiki</inherit><inherit>mod/wiki:overridelock</inherit><inherit>mod/workshop:addinstance</inherit><inherit>mod/workshop:allocate</inherit><inherit>mod/workshop:deletesubmissions</inherit><inherit>mod/workshop:editdimensions</inherit><inherit>mod/workshop:ignoredeadlines</inherit><inherit>mod/workshop:manageexamples</inherit><inherit>mod/workshop:overridegrades</inherit><inherit>mod/workshop:publishsubmissions</inherit><inherit>mod/workshop:switchphase</inherit><inherit>mod/workshop:viewallassessments</inherit><inherit>mod/workshop:viewallsubmissions</inherit><inherit>mod/workshop:viewreviewernames</inherit><inherit>moodle/ai:acceptpolicy</inherit><inherit>moodle/ai:fetchanyuserpolicystatus</inherit><inherit>moodle/ai:fetchpolicy</inherit><inherit>moodle/analytics:listinsights</inherit><inherit>moodle/analytics:listowninsights</inherit><inherit>moodle/analytics:managemodels</inherit><inherit>moodle/backup:anonymise</inherit><inherit>moodle/backup:backupactivity</inherit><inherit>moodle/backup:backupcourse</inherit><inherit>moodle/backup:backupsection</inherit><inherit>moodle/backup:backuptargetimport</inherit><inherit>moodle/backup:configure</inherit><inherit>moodle/backup:downloadfile</inherit><inherit>moodle/backup:userinfo</inherit><inherit>moodle/badges:awardbadge</inherit><inherit>moodle/badges:configurecriteria</inherit><inherit>moodle/badges:configuredetails</inherit><inherit>moodle/badges:configuremessages</inherit><inherit>moodle/badges:createbadge</inherit><inherit>moodle/badges:deletebadge</inherit><inherit>moodle/badges:earnbadge</inherit><inherit>moodle/badges:manageglobalsettings</inherit><inherit>moodle/badges:manageownbadges</inherit><inherit>moodle/badges:revokebadge</inherit><inherit>moodle/badges:viewawarded</inherit><inherit>moodle/badges:viewbadges</inherit><inherit>moodle/badges:viewotherbadges</inherit><inherit>moodle/block:edit</inherit><inherit>moodle/blog:create</inherit><inherit>moodle/blog:manageentries</inherit><inherit>moodle/blog:viewdrafts</inherit><inherit>moodle/calendar:manageentries</inherit><inherit>moodle/calendar:managegroupentries</inherit><inherit>moodle/calendar:manageownentries</inherit><inherit>moodle/category:manage</inherit><inherit>moodle/category:viewcourselist</inherit><inherit>moodle/category:viewhiddencategories</inherit><inherit>moodle/cohort:assign</inherit><inherit>moodle/cohort:configurecustomfields</inherit><inherit>moodle/cohort:manage</inherit><inherit>moodle/cohort:view</inherit><inherit>moodle/comment:delete</inherit><inherit>moodle/competency:competencygrade</inherit><inherit>moodle/competency:competencymanage</inherit><inherit>moodle/competency:competencyview</inherit><inherit>moodle/competency:coursecompetencyconfigure</inherit><inherit>moodle/competency:coursecompetencymanage</inherit><inherit>moodle/competency:coursecompetencyview</inherit><inherit>moodle/competency:evidencedelete</inherit><inherit>moodle/competency:plancomment</inherit><inherit>moodle/competency:plancommentown</inherit><inherit>moodle/competency:planmanage</inherit><inherit>moodle/competency:planmanagedraft</inherit><inherit>moodle/competency:planmanageown</inherit><inherit>moodle/competency:planmanageowndraft</inherit><inherit>moodle/competency:planrequestreview</inherit><inherit>moodle/competency:planrequestreviewown</inherit><inherit>moodle/competency:planreview</inherit><inherit>moodle/competency:planview</inherit><inherit>moodle/competency:planviewdraft</inherit><inherit>moodle/competency:planviewown</inherit><inherit>moodle/competency:planviewowndraft</inherit><inherit>moodle/competency:templatemanage</inherit><inherit>moodle/competency:templateview</inherit><inherit>moodle/competency:usercompetencycomment</inherit><inherit>moodle/competency:usercompetencycommentown</inherit><inherit>moodle/competency:usercompetencyrequestreview</inherit><inherit>moodle/competency:usercompetencyrequestreviewown</inherit><inherit>moodle/competency:usercompetencyreview</inherit><inherit>moodle/competency:usercompetencyview</inherit><inherit>moodle/competency:userevidencemanage</inherit><inherit>moodle/competency:userevidencemanageown</inherit><inherit>moodle/competency:userevidenceview</inherit><inherit>moodle/contentbank:access</inherit><inherit>moodle/contentbank:changelockedcustomfields</inherit><inherit>moodle/contentbank:configurecustomfields</inherit><inherit>moodle/contentbank:copyanycontent</inherit><inherit>moodle/contentbank:copycontent</inherit><inherit>moodle/contentbank:deleteanycontent</inherit><inherit>moodle/contentbank:deleteowncontent</inherit><inherit>moodle/contentbank:downloadcontent</inherit><inherit>moodle/contentbank:manageanycontent</inherit><inherit>moodle/contentbank:manageowncontent</inherit><inherit>moodle/contentbank:upload</inherit><inherit>moodle/contentbank:useeditor</inherit><inherit>moodle/contentbank:viewunlistedcontent</inherit><inherit>moodle/course:activityvisibility</inherit><inherit>moodle/course:bulkmessaging</inherit><inherit>moodle/course:changecategory</inherit><inherit>moodle/course:changefullname</inherit><inherit>moodle/course:changeidnumber</inherit><inherit>moodle/course:changelockedcustomfields</inherit><inherit>moodle/course:changeshortname</inherit><inherit>moodle/course:changesummary</inherit><inherit>moodle/course:configurecoursecommunication</inherit><inherit>moodle/course:configurecustomfields</inherit><inherit>moodle/course:configuredownloadcontent</inherit><inherit>moodle/course:create</inherit><inherit>moodle/course:creategroupconversations</inherit><inherit>moodle/course:delete</inherit><inherit>moodle/course:editcoursewelcomemessage</inherit><inherit>moodle/course:enrolconfig</inherit><inherit>moodle/course:enrolreview</inherit><inherit>moodle/course:ignoreavailabilityrestrictions</inherit><inherit>moodle/course:ignorefilesizelimits</inherit><inherit>moodle/course:manageactivities</inherit><inherit>moodle/course:managefiles</inherit><inherit>moodle/course:managegroups</inherit><inherit>moodle/course:managescales</inherit><inherit>moodle/course:markcomplete</inherit><inherit>moodle/course:movesections</inherit><inherit>moodle/course:overridecompletion</inherit><inherit>moodle/course:recommendactivity</inherit><inherit>moodle/course:renameroles</inherit><inherit>moodle/course:request</inherit><inherit>moodle/course:reset</inherit><inherit>moodle/course:reviewotherusers</inherit><inherit>moodle/course:sectionvisibility</inherit><inherit>moodle/course:setcurrentsection</inherit><inherit>moodle/course:setforcedlanguage</inherit><inherit>moodle/course:tag</inherit><inherit>moodle/course:togglecompletion</inherit><inherit>moodle/course:update</inherit><inherit>moodle/course:useremail</inherit><inherit>moodle/course:view</inherit><inherit>moodle/course:viewhiddenactivities</inherit><inherit>moodle/course:viewhiddencourses</inherit><inherit>moodle/course:viewhiddengroups</inherit><inherit>moodle/course:viewhiddensections</inherit><inherit>moodle/course:viewhiddenuserfields</inherit><inherit>moodle/course:viewsuspendedusers</inherit><inherit>moodle/course:visibility</inherit><inherit>moodle/filter:manage</inherit><inherit>moodle/grade:edit</inherit><inherit>moodle/grade:export</inherit><inherit>moodle/grade:hide</inherit><inherit>moodle/grade:import</inherit><inherit>moodle/grade:lock</inherit><inherit>moodle/grade:manage</inherit><inherit>moodle/grade:managegradingforms</inherit><inherit>moodle/grade:manageletters</inherit><inherit>moodle/grade:manageoutcomes</inherit><inherit>moodle/grade:managesharedforms</inherit><inherit>moodle/grade:sharegradingforms</inherit><inherit>moodle/grade:unlock</inherit><inherit>moodle/grade:viewall</inherit><inherit>moodle/grade:viewhidden</inherit><inherit>moodle/group:configurecustomfields</inherit><inherit>moodle/h5p:deploy</inherit><inherit>moodle/h5p:setdisplayoptions</inherit><inherit>moodle/h5p:updatelibraries</inherit><inherit>moodle/moodlenet:shareactivity</inherit><inherit>moodle/moodlenet:sharecourse</inherit><inherit>moodle/my:configsyspages</inherit><inherit>moodle/my:manageblocks</inherit><inherit>moodle/notes:manage</inherit><inherit>moodle/notes:view</inherit><inherit>moodle/payment:manageaccounts</inherit><inherit>moodle/payment:viewpayments</inherit><inherit>moodle/question:add</inherit><inherit>moodle/question:commentall</inherit><inherit>moodle/question:commentmine</inherit><inherit>moodle/question:config</inherit><inherit>moodle/question:editall</inherit><inherit>moodle/question:editmine</inherit><inherit>moodle/question:managecategory</inherit><inherit>moodle/question:moveall</inherit><inherit>moodle/question:movemine</inherit><inherit>moodle/question:tagall</inherit><inherit>moodle/question:tagmine</inherit><inherit>moodle/question:useall</inherit><inherit>moodle/question:usemine</inherit><inherit>moodle/question:viewall</inherit><inherit>moodle/question:viewmine</inherit><inherit>moodle/reportbuilder:edit</inherit><inherit>moodle/reportbuilder:editall</inherit><inherit>moodle/reportbuilder:scheduleviewas</inherit><inherit>moodle/reportbuilder:view</inherit><inherit>moodle/reportbuilder:viewall</inherit><inherit>moodle/restore:configure</inherit><inherit>moodle/restore:createuser</inherit><inherit>moodle/restore:restoreactivity</inherit><inherit>moodle/restore:restorecourse</inherit><inherit>moodle/restore:restoresection</inherit><inherit>moodle/restore:restoretargetimport</inherit><inherit>moodle/restore:rolldates</inherit><inherit>moodle/restore:uploadfile</inherit><inherit>moodle/restore:userinfo</inherit><inherit>moodle/restore:viewautomatedfilearea</inherit><inherit>moodle/role:assign</inherit><inherit>moodle/role:manage</inherit><inherit>moodle/role:override</inherit><inherit>moodle/role:review</inherit><inherit>moodle/role:safeoverride</inherit><inherit>moodle/role:switchroles</inherit><inherit>moodle/site:accessallgroups</inherit><inherit>moodle/site:approvecourse</inherit><inherit>moodle/site:config</inherit><inherit>moodle/site:configview</inherit><inherit>moodle/site:deleteanymessage</inherit><inherit>moodle/site:deleteownmessage</inherit><inherit>moodle/site:doclinks</inherit><inherit>moodle/site:forcelanguage</inherit><inherit>moodle/site:maintenanceaccess</inherit><inherit>moodle/site:manageallmessaging</inherit><inherit>moodle/site:manageblocks</inherit><inherit>moodle/site:managecontextlocks</inherit><inherit>moodle/site:messageanyuser</inherit><inherit>moodle/site:mnetlogintoremote</inherit><inherit>moodle/site:readallmessages</inherit><inherit>moodle/site:senderrormessage</inherit><inherit>moodle/site:sendmessage</inherit><inherit>moodle/site:trustcontent</inherit><inherit>moodle/site:uploadusers</inherit><inherit>moodle/site:viewanonymousevents</inherit><inherit>moodle/site:viewfullnames</inherit><inherit>moodle/site:viewparticipants</inherit><inherit>moodle/site:viewreports</inherit><inherit>moodle/site:viewuseridentity</inherit><inherit>moodle/tag:edit</inherit><inherit>moodle/tag:editblocks</inherit><inherit>moodle/tag:flag</inherit><inherit>moodle/tag:manage</inherit><inherit>moodle/user:changeownpassword</inherit><inherit>moodle/user:create</inherit><inherit>moodle/user:delete</inherit><inherit>moodle/user:editmessageprofile</inherit><inherit>moodle/user:editownmessageprofile</inherit><inherit>moodle/user:editownprofile</inherit><inherit>moodle/user:editprofile</inherit><inherit>moodle/user:ignoreuserquota</inherit><inherit>moodle/user:loginas</inherit><inherit>moodle/user:manageblocks</inherit><inherit>moodle/user:manageownblocks</inherit><inherit>moodle/user:manageownfiles</inherit><inherit>moodle/user:managesyspages</inherit><inherit>moodle/user:update</inherit><inherit>moodle/user:viewalldetails</inherit><inherit>moodle/user:viewhiddendetails</inherit><inherit>moodle/user:viewlastip</inherit><inherit>moodle/user:viewprofilepictures</inherit><inherit>moodle/user:viewuseractivitiesreport</inherit><inherit>moodle/webservice:createmobiletoken</inherit><inherit>moodle/webservice:createtoken</inherit><inherit>moodle/webservice:managealltokens</inherit><inherit>qbank/customfields:changelockedcustomfields</inherit><inherit>qbank/customfields:configurecustomfields</inherit><inherit>qbank/customfields:viewhiddencustomfields</inherit><inherit>quiz/grading:viewidnumber</inherit><inherit>quiz/grading:viewstudentnames</inherit><inherit>quiz/statistics:view</inherit><inherit>quizaccess/seb:bypassseb</inherit><inherit>quizaccess/seb:manage_filemanager_sebconfigfile</inherit><inherit>quizaccess/seb:manage_seb_activateurlfiltering</inherit><inherit>quizaccess/seb:manage_seb_allowcapturecamera</inherit><inherit>quizaccess/seb:manage_seb_allowcapturemicrophone</inherit><inherit>quizaccess/seb:manage_seb_allowedbrowserexamkeys</inherit><inherit>quizaccess/seb:manage_seb_allowreloadinexam</inherit><inherit>quizaccess/seb:manage_seb_allowspellchecking</inherit><inherit>quizaccess/seb:manage_seb_allowuserquitseb</inherit><inherit>quizaccess/seb:manage_seb_configuremanually</inherit><inherit>quizaccess/seb:manage_seb_enableaudiocontrol</inherit><inherit>quizaccess/seb:manage_seb_expressionsallowed</inherit><inherit>quizaccess/seb:manage_seb_expressionsblocked</inherit><inherit>quizaccess/seb:manage_seb_filterembeddedcontent</inherit><inherit>quizaccess/seb:manage_seb_linkquitseb</inherit><inherit>quizaccess/seb:manage_seb_muteonstartup</inherit><inherit>quizaccess/seb:manage_seb_quitpassword</inherit><inherit>quizaccess/seb:manage_seb_regexallowed</inherit><inherit>quizaccess/seb:manage_seb_regexblocked</inherit><inherit>quizaccess/seb:manage_seb_requiresafeexambrowser</inherit><inherit>quizaccess/seb:manage_seb_showkeyboardlayout</inherit><inherit>quizaccess/seb:manage_seb_showreloadbutton</inherit><inherit>quizaccess/seb:manage_seb_showsebdownloadlink</inherit><inherit>quizaccess/seb:manage_seb_showsebtaskbar</inherit><inherit>quizaccess/seb:manage_seb_showtime</inherit><inherit>quizaccess/seb:manage_seb_showwificontrol</inherit><inherit>quizaccess/seb:manage_seb_templateid</inherit><inherit>quizaccess/seb:manage_seb_userconfirmquit</inherit><inherit>quizaccess/seb:manage_seb_usesebclientconfig</inherit><inherit>quizaccess/seb:managetemplates</inherit><inherit>report/completion:view</inherit><inherit>report/courseoverview:view</inherit><inherit>report/log:view</inherit><inherit>report/log:viewtoday</inherit><inherit>report/loglive:view</inherit><inherit>report/outline:view</inherit><inherit>report/outline:viewuserreport</inherit><inherit>report/participation:view</inherit><inherit>report/performance:view</inherit><inherit>report/progress:view</inherit><inherit>report/questioninstances:view</inherit><inherit>report/security:view</inherit><inherit>report/stats:view</inherit><inherit>report/status:view</inherit><inherit>report/usersessions:manageownsessions</inherit><inherit>repository/areafiles:view</inherit><inherit>repository/contentbank:accesscoursecategorycontent</inherit><inherit>repository/contentbank:accesscoursecontent</inherit><inherit>repository/contentbank:accessgeneralcontent</inherit><inherit>repository/contentbank:view</inherit><inherit>repository/coursefiles:view</inherit><inherit>repository/dropbox:view</inherit><inherit>repository/equella:view</inherit><inherit>repository/filesystem:view</inherit><inherit>repository/flickr_public:view</inherit><inherit>repository/flickr:view</inherit><inherit>repository/googledocs:view</inherit><inherit>repository/local:view</inherit><inherit>repository/merlot:view</inherit><inherit>repository/nextcloud:view</inherit><inherit>repository/onedrive:view</inherit><inherit>repository/recent:view</inherit><inherit>repository/s3:view</inherit><inherit>repository/upload:view</inherit><inherit>repository/url:view</inherit><inherit>repository/user:view</inherit><inherit>repository/webdav:view</inherit><inherit>repository/wikimedia:view</inherit><inherit>repository/youtube:view</inherit><inherit>tiny/h5p:addembed</inherit><inherit>tiny/premium:accesspremium</inherit><inherit>tiny/recordrtc:recordaudio</inherit><inherit>tiny/recordrtc:recordscreen</inherit><inherit>tiny/recordrtc:recordvideo</inherit><inherit>tool/brickfield:viewcoursetools</inherit><inherit>tool/brickfield:viewsystemtools</inherit><inherit>tool/customlang:edit</inherit><inherit>tool/customlang:export</inherit><inherit>tool/customlang:view</inherit><inherit>tool/dataprivacy:downloadallrequests</inherit><inherit>tool/dataprivacy:downloadownrequest</inherit><inherit>tool/dataprivacy:makedatadeletionrequestsforchildren</inherit><inherit>tool/dataprivacy:makedatarequestsforchildren</inherit><inherit>tool/dataprivacy:managedataregistry</inherit><inherit>tool/dataprivacy:managedatarequests</inherit><inherit>tool/dataprivacy:requestdelete</inherit><inherit>tool/dataprivacy:requestdeleteforotheruser</inherit><inherit>tool/iomadmerge:iomadmerge</inherit><inherit>tool/iomadpolicy:accept</inherit><inherit>tool/iomadpolicy:acceptbehalf</inherit><inherit>tool/iomadpolicy:managedocs</inherit><inherit>tool/iomadpolicy:viewacceptances</inherit><inherit>tool/lpmigrate:frameworksmigrate</inherit><inherit>tool/mfa:mfaaccess</inherit><inherit>tool/monitor:managerules</inherit><inherit>tool/monitor:managetool</inherit><inherit>tool/monitor:subscribe</inherit><inherit>tool/policy:accept</inherit><inherit>tool/policy:acceptbehalf</inherit><inherit>tool/policy:managedocs</inherit><inherit>tool/policy:viewacceptances</inherit><inherit>tool/recyclebin:deleteitems</inherit><inherit>tool/recyclebin:restoreitems</inherit><inherit>tool/recyclebin:viewitems</inherit><inherit>tool/redocerts:redocertificates</inherit><inherit>tool/uploadcourse:use</inherit><inherit>tool/uploaduser:uploaduserpictures</inherit><inherit>tool/usertours:managetours</inherit><inherit>webservice/rest:use</inherit><inherit>webservice/soap:use</inherit><allow>aiplacement/courseassist:summarise_text</allow><allow>aiplacement/editor:generate_image</allow><allow>aiplacement/editor:generate_text</allow><allow>block/iomad_onlineusers:viewlist</allow><allow>block/online_users:viewlist</allow><allow>booktool/print:print</allow><allow>enrol/license:unenrolself</allow><allow>enrol/self:unenrolself</allow><allow>gradereport/user:view</allow><allow>mod/assign:exportownsubmission</allow><allow>mod/assign:submit</allow><allow>mod/assign:view</allow><allow>mod/assign:viewownsubmissionsummary</allow><allow>mod/bigbluebuttonbn:join</allow><allow>mod/bigbluebuttonbn:view</allow><allow>mod/book:read</allow><allow>mod/chat:chat</allow><allow>mod/chat:readlog</allow><allow>mod/choice:choose</allow><allow>mod/data:comment</allow><allow>mod/data:exportownentry</allow><allow>mod/data:view</allow><allow>mod/data:viewentry</allow><allow>mod/data:writeentry</allow><allow>mod/feedback:complete</allow><allow>mod/feedback:view</allow><allow>mod/feedback:viewanalysepage</allow><allow>mod/forum:allowforcesubscribe</allow><allow>mod/forum:canmailnow</allow><allow>mod/forum:createattachment</allow><allow>mod/forum:deleteownpost</allow><allow>mod/forum:exportownpost</allow><allow>mod/forum:replypost</allow><allow>mod/forum:startdiscussion</allow><allow>mod/forum:viewdiscussion</allow><allow>mod/forum:viewrating</allow><allow>mod/glossary:comment</allow><allow>mod/glossary:exportownentry</allow><allow>mod/glossary:view</allow><allow>mod/glossary:write</allow><allow>mod/h5pactivity:submit</allow><allow>mod/h5pactivity:view</allow><allow>mod/hvp:getcachedassets</allow><allow>mod/hvp:savecontentuserdata</allow><allow>mod/hvp:saveresults</allow><allow>mod/hvp:view</allow><allow>mod/hvp:viewresults</allow><allow>mod/iomadcertificate:view</allow><allow>mod/lti:view</allow><allow>mod/quiz:attempt</allow><allow>mod/quiz:reviewmyattempts</allow><allow>mod/quiz:view</allow><allow>mod/scorm:savetrack</allow><allow>mod/scorm:skipview</allow><allow>mod/scorm:viewscores</allow><allow>mod/survey:participate</allow><allow>mod/wiki:createpage</allow><allow>mod/wiki:editcomment</allow><allow>mod/wiki:editpage</allow><allow>mod/wiki:viewcomment</allow><allow>mod/wiki:viewpage</allow><allow>mod/workshop:exportsubmissions</allow><allow>mod/workshop:peerassess</allow><allow>mod/workshop:submit</allow><allow>mod/workshop:view</allow><allow>mod/workshop:viewauthornames</allow><allow>mod/workshop:viewauthorpublished</allow><allow>mod/workshop:viewpublishedsubmissions</allow><allow>moodle/block:view</allow><allow>moodle/blog:manageexternal</allow><allow>moodle/blog:search</allow><allow>moodle/blog:view</allow><allow>moodle/comment:post</allow><allow>moodle/comment:view</allow><allow>moodle/competency:coursecompetencygradable</allow><allow>moodle/course:downloadcoursecontent</allow><allow>moodle/course:isincompletionreports</allow><allow>moodle/course:viewparticipants</allow><allow>moodle/course:viewscales</allow><allow>moodle/grade:view</allow><allow>moodle/portfolio:export</allow><allow>moodle/question:flag</allow><allow>moodle/rating:rate</allow><allow>moodle/rating:view</allow><allow>moodle/rating:viewall</allow><allow>moodle/rating:viewany</allow><allow>moodle/search:query</allow><allow>moodle/user:readuserblogs</allow><allow>moodle/user:readuserposts</allow><allow>moodle/user:viewdetails</allow></permissions></role>
