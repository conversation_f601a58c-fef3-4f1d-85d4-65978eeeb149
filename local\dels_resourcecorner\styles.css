.local_dels_resourcecorner .resource-list {
    max-width: 800px;
    margin: 0 auto;
}

.local_dels_resourcecorner .resource-item {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.local_dels_resourcecorner .video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
    overflow: hidden;
}

.local_dels_resourcecorner .video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.local_dels_resourcecorner-edit .mform {
    max-width: 800px;
    margin: 0 auto;
}

.local_dels_resourcecorner-edit .fitem {
    margin-bottom: 1rem;
}

.local_dels_resourcecorner-edit .fitemtitle {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.local_dels_resourcecorner-edit .felement {
    width: 100%;
}

.local_dels_resourcecorner-edit input[type="text"],
.local_dels_resourcecorner-edit select,
.local_dels_resourcecorner-edit textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.local_dels_resourcecorner-edit .fsubmit {
    margin-top: 1.5rem;
    text-align: center;
}

.local_dels_resourcecorner .resource-index {
    width: 40px;
    height: 40px;
    font-weight: bold;
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.local_dels_resourcecorner .resource-item {
    border-left: 4px solid #007bff;
}

.local_dels_resourcecorner .resource-info {
    flex-grow: 1;
}

/* 管理页面样式 */
.local_dels_resourcecorner .action-icon {
    margin: 0 5px;
    display: inline-block;
}

.local_dels_resourcecorner .download-icon {
    color: #28a745; /* 绿色下载图标 */
}

.local_dels_resourcecorner .delete-icon {
    color: #dc3545; /* 红色删除图标 */
}

.local_dels_resourcecorner .action-icon:hover {
    opacity: 0.7;
    text-decoration: none;
}

/* 过滤器表单样式 */
#resourcefilterform {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

#resourcefilterform .fitem {
    margin-bottom: 10px;
}

#resourcefilterform .fitemtitle {
    font-weight: bold;
    margin-bottom: 5px;
}

#resourcefilterform .felement {
    width: 100%;
}

#resourcefilterform input[type="text"],
#resourcefilterform select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ced4da;
    border-radius: 3px;
}

#resourcefilterform .fgroup {
    display: flex;
    gap: 10px;
}

#resourcefilterform .fgroup .fitem {
    flex: 1;
}

#resourcefilterform .fsubmit {
    margin-top: 15px;
    text-align: right;
}

#resourcefilterform .fsubmit input {
    margin-left: 10px;
}

/* 结果计数样式 */
.resource-count {
    font-weight: bold;
    margin-top: 15px;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
}

/* 过滤器表单样式 */
.local_dels_resourcecorner .mform {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.local_dels_resourcecorner .mform .fitem {
    margin-bottom: 10px;
}

.local_dels_resourcecorner .mform .fitemtitle {
    font-weight: bold;
    margin-bottom: 5px;
}

.local_dels_resourcecorner .mform .felement {
    width: 100%;
}

.local_dels_resourcecorner .mform input[type="text"],
.local_dels_resourcecorner .mform select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ced4da;
    border-radius: 3px;
}

.local_dels_resourcecorner .mform .fgroup {
    display: flex;
    gap: 10px;
}

.local_dels_resourcecorner .mform .fgroup .fitem {
    flex: 1;
}

.local_dels_resourcecorner .mform .fsubmit {
    margin-top: 15px;
    text-align: right;
}

.local_dels_resourcecorner .mform .fsubmit input {
    margin-left: 10px;
}

/* 过滤器容器样式 */
.local_dels_resourcecorner .filter-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.local_dels_resourcecorner .filter-container .card-header {
    font-weight: bold;
    padding: 0.75rem 1.25rem;
}

.local_dels_resourcecorner .filter-container .card-body {
    padding: 1.25rem;
}

/* 过滤器表单样式 */
.local_dels_resourcecorner .filter-container .mform {
    margin-bottom: 0;
}

.local_dels_resourcecorner .filter-container .mform .fitem {
    margin-bottom: 1rem;
}

.local_dels_resourcecorner .filter-container .mform .fitemtitle {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.local_dels_resourcecorner .filter-container .mform .felement {
    width: 100%;
}

.local_dels_resourcecorner .filter-container .mform input[type="text"],
.local_dels_resourcecorner .filter-container .mform select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
}

.local_dels_resourcecorner .filter-container .mform .fsubmit {
    margin-top: 1rem;
    text-align: right;
}

.local_dels_resourcecorner .filter-container .mform .fsubmit input {
    margin-left: 0.5rem;
}

/* 结果计数样式 */
.local_dels_resourcecorner .resource-count {
    font-weight: bold;
    margin-top: 1rem;
    padding: 0.75rem 1.25rem;
    border-radius: 0.25rem;
}

.local_dels_resourcecorner .edit-icon {
    color: #007bff; /* 蓝色编辑图标 */
}

.local_dels_resourcecorner .edit-icon:hover {
    opacity: 0.7;
    text-decoration: none;
}