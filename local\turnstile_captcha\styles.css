/**
 * Styles for the Turnstile CAPTCHA local plugin.
 *
 * @package    local_turnstile_captcha
 * @copyright  2024 DELS CPT
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

/* CAPTCHA widget styling */
.cf-turnstile {
    margin: 10px 0;
    display: flex;
    justify-content: center;
}

/* CAPTCHA header styling */
#fgroup_id_captcha_header {
    margin-top: 20px;
}

/* Error message styling for CAPTCHA */
.form-group .error[data-fieldname="captcha_header"] {
    color: #d32f2f;
    font-weight: bold;
    margin-top: 5px;
}

/* Responsive design for mobile devices */
@media (max-width: 768px) {
    .cf-turnstile {
        transform: scale(0.8);
        transform-origin: center;
    }
}
